"""
Générateur du diagramme en cascade (waterfall) pour la décomposition du LCOE
du projet solaire PV 10 MW au Maroc.

Ce script crée une visualisation montrant comment les différentes incitations
réduisent le LCOE brut pour arriver au LCOE net.
"""

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import matplotlib.patches as mpatches
from matplotlib.ticker import FuncFormatter

# Configuration
plt.rcParams.update({
    'font.family': 'Calibri, sans-serif',
    'font.size': 11,
    'figure.figsize': (10, 6),
    'figure.dpi': 300,
    'axes.titlesize': 14,
    'axes.titleweight': 'bold',
    'axes.labelsize': 12,
    'xtick.labelsize': 10,
    'ytick.labelsize': 10,
    'legend.fontsize': 10,
})

# Données pour le waterfall chart
# Les valeurs représentent la contribution de chaque élément au LCOE final
data = {
    'labels': [
        'LCOE brut',
        'Subvention IT\n(Piano Mattei)',
        'Subvention\nMASEN (10%)',
        'Subvention\nIRESEN (5%)',
        'Support\nraccordement',
        'Primes MA\n(Charte Invest.)',
        'Exonération TVA\nMA',
        'Exonération IS\n(5 ans)',
        'IS réduit\n(15%)',
        'LCOE net'
    ],
    'values': [0.064, -0.005, -0.003, -0.0015, -0.001, -0.0015, -0.002, -0.003, -0.001, 0.047],
    'colors': ['#1f77b4', '#2ca02c', '#9467bd', '#9467bd', '#9467bd', '#ff7f0e', '#ff7f0e', '#ff7f0e', '#ff7f0e', '#d62728'],
    'is_total': [True, False, False, False, False, False, False, False, False, True]
}

# Calcul des positions intermédiaires
def calculate_positions(values, is_total):
    positions = []
    current_sum = values[0]
    positions.append(current_sum)
    
    for i in range(1, len(values)-1):
        if not is_total[i]:
            current_sum += values[i]
        positions.append(current_sum)
    
    positions.append(values[-1])  # Dernière valeur est le total final
    return positions

# Création de la figure
fig, ax = plt.subplots(figsize=(10, 6), facecolor='#f9f9f9')
ax.set_facecolor('#f9f9f9')

# Ajout d'une bordure fine
for spine in ax.spines.values():
    spine.set_color('#bbbbbb')
    spine.set_linewidth(0.8)

# Calcul des positions
positions = calculate_positions(data['values'], data['is_total'])

# Création des barres pour le waterfall chart
bars = []
for i, (label, value, color, is_tot) in enumerate(zip(
    data['labels'], data['values'], data['colors'], data['is_total'])):
    
    if is_tot:
        # Barre totale
        bar = ax.bar(i, value, width=0.6, color=color, 
                    alpha=0.9, edgecolor='white', linewidth=1)
        bars.append(bar)
    else:
        # Barre intermédiaire (contribution)
        bar = ax.bar(i, value, bottom=positions[i-1], width=0.6, color=color, 
                    alpha=0.8, edgecolor='white', linewidth=1)
        bars.append(bar)
        
        # Lignes pointillées pour connecter les barres
        if i < len(data['labels'])-1:
            ax.plot([i-0.3, i+0.3], [positions[i], positions[i]], 
                   color='#555555', linestyle='--', linewidth=1, alpha=0.5)

# Étiquettes des axes et titre
ax.set_ylabel('LCOE (€/kWh)', fontweight='bold')
ax.set_title('Décomposition du LCOE : Impact des Incitations Italie-Maroc', 
           pad=15, fontweight='bold')

# Étiquettes des barres
ax.set_xticks(range(len(data['labels'])))
ax.set_xticklabels(data['labels'], rotation=0)

# Formatter pour afficher les valeurs en €/kWh
def format_euros(x, pos):
    return f'{x:.3f} €/kWh'

ax.yaxis.set_major_formatter(FuncFormatter(format_euros))

# Afficher les valeurs sur les barres
for i, (bar, value) in enumerate(zip(bars, data['values'])):
    height = bar[0].get_height()
    text_color = 'white' if data['is_total'][i] else 'black'
    y_pos = bar[0].get_y() + height/2
    
    if data['is_total'][i]:
        ax.text(i, y_pos, f'{abs(value):.3f} €/kWh', ha='center', va='center',
               fontsize=11, fontweight='bold', color=text_color)
    else:
        sign = '+' if value > 0 else ''
        ax.text(i, y_pos, f'{sign}{value:.3f} €/kWh', ha='center', va='center',
               fontsize=10, color=text_color)

# Légende par origine des incitations
colors_legend = {
    'LCOE': '#1f77b4',
    'Incitations italiennes': '#2ca02c',
    'Incitations MASEN/IRESEN': '#9467bd',
    'Incitations marocaines (autres)': '#ff7f0e',
    'LCOE final': '#d62728'
}

patches = [mpatches.Patch(color=color, label=label) for label, color in colors_legend.items()]
ax.legend(handles=patches, loc='upper center', bbox_to_anchor=(0.5, -0.12),
         ncol=4, frameon=True, facecolor='white', edgecolor='#bbbbbb')

# Grille
ax.grid(axis='y', linestyle='--', alpha=0.3)

# Annotations
ax.text(0.05, 0.02, 
       "Réduction totale du LCOE : -0,017 €/kWh (-26,6%)", 
       transform=fig.transFigure, fontsize=9, style='italic')
ax.text(0.05, 0.05, 
       "Impact MASEN/IRESEN : -0,0055 €/kWh (-8,6%)", 
       transform=fig.transFigure, fontsize=9, style='italic', color='#9467bd')

plt.tight_layout(pad=2.0)

# Sauvegarder l'image
plt.savefig('figures/lcoe_waterfall.png', dpi=300, bbox_inches='tight', 
           facecolor='#f9f9f9')
print("Diagramme waterfall LCOE généré avec succès!")
plt.close()
