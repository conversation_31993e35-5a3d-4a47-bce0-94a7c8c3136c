#!/usr/bin/env python3
"""
Standalone DOCX Export Utility for Enhanced Financial Model
This utility can be used to export financial reports to DOCX format
"""

import sys
from datetime import datetime
from pathlib import Path

def check_docx_availability():
    """Check if python-docx is available"""
    try:
        from docx import Document
        from docx.shared import Inches, Pt
        from docx.enum.text import WD_ALIGN_PARAGRAPH
        from docx.enum.table import WD_TABLE_ALIGNMENT
        return True, None
    except ImportError as e:
        return False, str(e)

def install_docx_instructions():
    """Provide installation instructions for python-docx"""
    instructions = """
🔧 DOCX Export Setup Required

The python-docx library is not installed. To enable DOCX export functionality:

1. Open your terminal/command prompt
2. Run one of these commands:

   For pip users:
   pip install python-docx

   For conda users:
   conda install python-docx

   For pip3 users:
   pip3 install python-docx

3. Restart the application after installation

Alternative: Install all requirements at once:
pip install -r requirements_enhanced.txt
"""
    return instructions

def create_sample_docx_report(filename=None, reports_dir=None):
    """Create a sample DOCX report to test functionality"""
    
    # Check availability first
    available, error = check_docx_availability()
    if not available:
        print(f"❌ python-docx not available: {error}")
        print(install_docx_instructions())
        return False
    
    try:
        from docx import Document
        from docx.shared import Inches
        from docx.enum.text import WD_ALIGN_PARAGRAPH
        from docx.enum.table import WD_TABLE_ALIGNMENT
        
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            if reports_dir:
                # Ensure reports directory exists
                Path(reports_dir).mkdir(parents=True, exist_ok=True)
                filename = Path(reports_dir) / f"sample_financial_report_{timestamp}.docx"
            else:
                filename = f"sample_financial_report_{timestamp}.docx"
        
        # Create new document
        doc = Document()
        
        # Add title
        title = doc.add_heading('Enhanced Financial Model Report', 0)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # Add subtitle
        subtitle = doc.add_heading('Sample Project: Morocco Solar PV', level=1)
        subtitle.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # Add executive summary
        doc.add_heading('Executive Summary', level=1)
        
        # Project details table
        table = doc.add_table(rows=1, cols=2)
        table.style = 'Table Grid'
        table.alignment = WD_TABLE_ALIGNMENT.CENTER
        
        # Header row
        hdr_cells = table.rows[0].cells
        hdr_cells[0].text = 'Parameter'
        hdr_cells[1].text = 'Value'
        
        # Add project data
        project_data = [
            ('Project Name', 'Morocco Solar PV Project'),
            ('Technology', 'Solar Photovoltaic'),
            ('Capacity', '10.0 MW'),
            ('Location', 'Morocco'),
            ('Total Investment', '€8.5M'),
            ('Grant Support', '€2.8M (32.9% of CAPEX)'),
            ('Project Life', '25 years'),
            ('Annual Production (Year 1)', '22,000 MWh'),
            ('PPA Price', '0.055 EUR/kWh')
        ]
        
        for param, value in project_data:
            row_cells = table.add_row().cells
            row_cells[0].text = param
            row_cells[1].text = str(value)
        
        # Add KPIs section
        doc.add_heading('Key Performance Indicators', level=1)
        
        kpi_table = doc.add_table(rows=1, cols=2)
        kpi_table.style = 'Table Grid'
        kpi_table.alignment = WD_TABLE_ALIGNMENT.CENTER
        
        # KPI header
        kpi_hdr = kpi_table.rows[0].cells
        kpi_hdr[0].text = 'KPI'
        kpi_hdr[1].text = 'Value'
        
        # Add KPI data
        kpi_data = [
            ('Project IRR', '15.2%'),
            ('Equity IRR', '18.5%'),
            ('LCOE', '0.051 EUR/kWh'),
            ('NPV (Project)', '€7.3M'),
            ('NPV (Equity)', '€4.1M'),
            ('Minimum DSCR', '1.25'),
            ('Payback Period', '8.5 years')
        ]
        
        for kpi, value in kpi_data:
            kpi_row = kpi_table.add_row().cells
            kpi_row[0].text = kpi
            kpi_row[1].text = value
        
        # Add financing structure
        doc.add_heading('Financing Structure', level=1)
        
        financing_table = doc.add_table(rows=1, cols=3)
        financing_table.style = 'Table Grid'
        financing_table.alignment = WD_TABLE_ALIGNMENT.CENTER
        
        fin_hdr = financing_table.rows[0].cells
        fin_hdr[0].text = 'Source'
        fin_hdr[1].text = 'Amount (€M)'
        fin_hdr[2].text = 'Percentage'
        
        financing_data = [
            ('Equity', '2.8', '32.9%'),
            ('Debt', '2.9', '34.1%'),
            ('Grants', '2.8', '32.9%'),
            ('TOTAL', '8.5', '100.0%')
        ]
        
        for source, amount, percentage in financing_data:
            fin_row = financing_table.add_row().cells
            fin_row[0].text = source
            fin_row[1].text = amount
            fin_row[2].text = percentage
        
        # Add analysis section
        doc.add_heading('Financial Analysis', level=1)
        
        analysis_text = """
This renewable energy project in Morocco demonstrates strong financial viability with the following key highlights:

• The project achieves an equity IRR of 18.5%, which exceeds typical investor return requirements for emerging market renewable energy projects (12-15%).

• The LCOE of 0.051 EUR/kWh is competitive with regional benchmarks and supports long-term project sustainability.

• Grant funding totaling €2.8M (32.9% of CAPEX) significantly improves project economics, reducing the equity requirement and enhancing returns.

• The minimum DSCR of 1.25 meets typical lender covenant requirements.

• The project benefits from Morocco's supportive renewable energy framework and Italy's strategic focus on African energy investments through the Mattei Plan.
"""
        
        doc.add_paragraph(analysis_text)
        
        # Add risk factors
        doc.add_heading('Key Risk Factors', level=1)
        
        risk_text = """
• Regulatory Risk: Changes in Moroccan renewable energy policies or Italian grant programs
• Currency Risk: EUR/MAD exchange rate fluctuations affecting local costs and revenues
• Technology Risk: Solar panel degradation rates and O&M cost escalation
• Market Risk: Electricity price evolution and PPA renegotiation risks
• Political Risk: Changes in bilateral agreements between Italy and Morocco
"""
        
        doc.add_paragraph(risk_text)
        
        # Add recommendations
        doc.add_heading('Recommendations', level=1)
        
        recommendations_text = """
• Proceed with project development given strong financial metrics and grant support
• Secure grant commitments early in the development process to reduce financing risk
• Consider hedging strategies for currency and interest rate exposure
• Implement robust O&M contracts to ensure performance targets are met
• Monitor regulatory developments in both Morocco and Italy
"""
        
        doc.add_paragraph(recommendations_text)
        
        # Add footer with timestamp
        doc.add_page_break()
        footer_para = doc.add_paragraph()
        footer_para.add_run(f'Report generated on {datetime.now().strftime("%B %d, %Y at %H:%M")}')
        footer_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # Save document
        doc.save(filename)
        
        print(f"✅ DOCX report created successfully: {filename}")
        print(f"📁 Location: {Path(filename).absolute()}")
        return True
        
    except Exception as e:
        print(f"❌ DOCX report creation failed: {str(e)}")
        return False

def main():
    """Main function to test DOCX export"""
    print("🔬 DOCX Export Utility for Enhanced Financial Model")
    print("=" * 55)
    
    # Check if python-docx is available
    available, error = check_docx_availability()
    
    if available:
        print("✅ python-docx is available")
        print("\n📄 Creating sample financial report...")
        
        success = create_sample_docx_report()
        
        if success:
            print("\n🎉 DOCX export functionality is working!")
            print("✅ You can now use DOCX export in the main application")
        else:
            print("\n⚠️ DOCX export encountered an error")
    else:
        print(f"❌ python-docx not available: {error}")
        print(install_docx_instructions())
        
        # Try to install automatically (optional)
        try_install = input("\n🤔 Would you like to try installing python-docx now? (y/n): ").lower().strip()
        if try_install in ['y', 'yes']:
            print("\n🔧 Attempting to install python-docx...")
            import subprocess
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", "python-docx"])
                print("✅ Installation successful! Please restart the application.")
            except subprocess.CalledProcessError as e:
                print(f"❌ Installation failed: {e}")
                print("Please install manually using the instructions above.")

if __name__ == "__main__":
    main()
