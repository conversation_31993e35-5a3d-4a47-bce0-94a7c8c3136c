from __future__ import annotations
from dataclasses import dataclass, field
from typing import List # Keep List if used elsewhere, or remove if only for field example

@dataclass
class Assumptions:
    """Key inputs – adjust to test scenarios."""

    # Project lifetime & General
    years: int = 25
    location_name: str = "Ouarzazate"  # Descriptive, can be used to prefill or for non-core logic

    # Core Financial & Technical Parameters (user-adjustable, can be pre-filled by location_name)
    production_mwh_year1: float = 18_000  # P50 first-year production (MWh)
    degradation: float = 0.004  # 0.4 %/year
    ppa_price_eur_kwh: float = 0.075
    price_escalation: float = 0.02  # EU inflation
    opex_keuros_year1: float = 285  # year-1 OPEX (€k)
    opex_escalation: float = 0.02
    capex_meur: float = 9.5 # Total CAPEX in MEUR

    # Incentives breakdown (can also be made user-adjustable if needed)
    grant_meur_italy: float = 0.95  # Piano Mattei subsidy
    grant_meur_masen: float = 0.95  # ~10% of CAPEX for priority zones (example, could be % of CAPEX)
    grant_meur_iresen: float = 0.475 # ~5% for innovation components
    grant_meur_connection: float = 0.285 # 30% of connection costs (example)
    # Note: grant_meur_maroc was a sum, now individual components are listed.
    # The logic in build_cashflow will need to sum these.

    # Debt
    debt_ratio: float = 0.60 # Debt as % of (CAPEX - relevant grants)
    interest_rate: float = 0.025
    debt_years: int = 15
    grace_years: int = 2

    # Taxes
    tax_holiday: int = 5
    tax_rate: float = 0.15 # After holiday

    # Discount Rate for NPV
    discount_rate: float = 0.08

    # Removed location-specific properties like:
    # production_mwh_ouarzazate, production_mwh_dakhla
    # opex_keuros_ouarzazate, opex_keuros_dakhla
    # capex_meur_ouarzazate, capex_meur_dakhla
    # The UI will now manage setting these based on a location dropdown or direct input.

    # Example of how grant_meur_maroc could be calculated if needed within the class
    # This assumes grant_meur_masen, iresen, connection are direct inputs
    @property
    def total_grant_meur_maroc(self) -> float:
        return self.grant_meur_masen + self.grant_meur_iresen + self.grant_meur_connection

    @property
    def total_grants_meur(self) -> float:
        return self.grant_meur_italy + self.total_grant_meur_maroc

    # Initial investment net of all grants for debt sizing
    @property
    def investment_for_debt_sizing_meur(self) -> float:
        # Example: Debt is sized on CAPEX after all grants.
        # This might need adjustment based on which grants reduce the debt base.
        # For simplicity, let's assume all grants reduce the base for debt calculation.
        return self.capex_meur - self.total_grants_meur

    # If some grants do not reduce the debt base, adjust the above property.
    # For instance, if Italian grant doesn't reduce Moroccan debt base:
    # return self.capex_meur - self.total_grant_meur_maroc

    # No direct properties for production_mwh, opex_keuros, capex_meur anymore
    # as they are now base fields. 