\relax 
\providecommand\hyper@newdestlabel[2]{}
\providecommand\HyField@AuxAddToFields[1]{}
\providecommand\HyField@AuxAddToCoFields[2]{}
\providecommand\babel@aux[2]{}
\@nameuse{bbl@beforestart}
\catcode `:\active 
\catcode `;\active 
\catcode `!\active 
\catcode `?\active 
\providecommand \oddpage@label [2]{}
\babel@aux{french}{}
\@writefile{nav}{\headcommand {\slideentry {0}{0}{1}{1/1}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {1}{1}}}
\@writefile{nav}{\headcommand {\slideentry {0}{0}{2}{2/2}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {2}{2}}}
\@writefile{toc}{\beamer@sectionintoc {1}{Introduction et Contexte}{3}{0}{1}}
\@writefile{nav}{\headcommand {\beamer@sectionpages {1}{2}}}
\@writefile{nav}{\headcommand {\beamer@subsectionpages {1}{2}}}
\@writefile{nav}{\headcommand {\sectionentry {1}{Introduction et Contexte}{3}{Introduction et Contexte}{0}}}
\@writefile{nav}{\headcommand {\slideentry {1}{0}{1}{3/3}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {3}{3}}}
\@writefile{nav}{\headcommand {\slideentry {1}{0}{2}{4/4}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {4}{4}}}
\@writefile{nav}{\headcommand {\beamer@partpages {1}{4}}}
\@writefile{nav}{\headcommand {\beamer@subsectionpages {3}{4}}}
\@writefile{nav}{\headcommand {\beamer@sectionpages {3}{4}}}
\@writefile{nav}{\headcommand {\beamer@documentpages {4}}}
\@writefile{nav}{\headcommand {\gdef \inserttotalframenumber {4}}}
\gdef \@abspage@last{4}
