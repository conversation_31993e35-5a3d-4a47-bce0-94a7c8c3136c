% Piano Mattei Funding Distribution - Premium Professional Version

% Define premium color scheme with better contrast and visual appeal
\definecolor{climatefund}{RGB}{242,113,33}      % Vibrant orange for climate fund
\definecolor{devfund}{RGB}{0,166,156}          % <PERSON> teal for development fund
\definecolor{textwhite}{RGB}{255,255,255}      % Pure white for text
\definecolor{sourcegray}{RGB}{120,120,120}     % Subtle gray for source text

\begin{tikzpicture}
    % Remove the title as it's redundant with the figure caption in the main document
    
    % Create a clean circular background for the pie chart
    \fill[white, drop shadow={opacity=0.25, shadow xshift=0.7mm, shadow yshift=-0.7mm}] (0,0) circle (3.2cm);
    
    % Professional pie chart with enhanced styling
    % First slice - Climate fund (55%)
    \begin{scope}
        % Create slice with gradient effect
        \fill[top color=climatefund!90, bottom color=climatefund!100, shading angle=45] 
             (0,0) -- (0:3) arc (0:198:3) -- cycle;
        
        % Add subtle border to slice
        \draw[climatefund!70!black, line width=0.4pt] (0,0) -- (0:3) arc (0:198:3) -- cycle;
        
        % Enhanced label for climate fund with better positioning
        \node[font=\bfseries, text=textwhite, align=center] at (-1.3,1.1) {Fonds italien};
        \node[font=\bfseries, text=textwhite, align=center] at (-1.3,0.7) {pour le climat};
        \node[font=\bfseries\small, text=textwhite, align=center] at (-1.3,0.2) {3 milliards €};
    \end{scope}
    
    % Second slice - Development fund (45%)
    \begin{scope}
        % Create slice with gradient effect
        \fill[top color=devfund!90, bottom color=devfund!100, shading angle=225] 
             (0,0) -- (198:3) arc (198:360:3) -- cycle;
        
        % Add subtle border to slice
        \draw[devfund!70!black, line width=0.4pt] (0,0) -- (198:3) arc (198:360:3) -- cycle;
        
        % Enhanced label for development fund with better positioning
        \node[font=\bfseries, text=textwhite, align=center] at (1.3,-0.7) {Fonds de coopération};
        \node[font=\bfseries, text=textwhite, align=center] at (1.3,-1.1) {au développement};
        \node[font=\bfseries\small, text=textwhite, align=center] at (1.3,-1.6) {2,5 milliards €};
    \end{scope}
    
    % Total amount with enhanced styling
    \node[font=\bfseries\large, align=center] at (0,-3.3) {Total : 5,5 milliards d'euros};
    
    % Source with professional styling
    \node[font=\footnotesize, text=sourcegray, align=center] at (0,-3.9) {Source : Piano Mattei per l'Africa, Gouvernement Italien};
    
    % Enhanced legend with professional styling
    \begin{scope}[shift={(4.2,0)}]
        % Climate fund legend item
        \fill[climatefund, rounded corners=1pt] (0,0.7) rectangle (0.5,1.1);
        \node[anchor=west, font=\small] at (0.7,0.9) {Fonds italien pour le climat (55\%)};
        
        % Development fund legend item
        \fill[devfund, rounded corners=1pt] (0,-0.1) rectangle (0.5,0.3);
        \node[anchor=west, font=\small] at (0.7,0.1) {Fonds de coopération au développement (45\%)};
    \end{scope}
\end{tikzpicture}