"""generate_figures.py

Ce script lit le modèle financier Excel généré par `financial_model.py` et
produit plusieurs visuels (PNG haute résolution, 300 dpi) prêts à être
intégrés dans le rapport LaTeX.  Les étiquettes et titres sont rédigés en
français.

Graphiques produits (dans le dossier `figures/`):
  1. fcfe_cumule.png – Flux de trésorerie disponible pour les actionnaires (cumulé)
  2. dscr.png – Couverture du service de la dette (DSCR) vs covenant 1,20
  3. sens_tri.png – Tornado-chart : impact des variations du tarif PPA sur le TRI
  4. structure_financement.png – Composition initiale du financement (subventions, dette, capitaux propres)

Dépendances : pandas, matplotlib, seaborn, numpy.

Exécution :
    python generate_figures.py [chemin_vers_excel]

Si aucun argument n'est fourni, le script suppose `solar_financial_model.xlsx`
présent dans le même répertoire.
"""
from __future__ import annotations

import sys
from pathlib import Path

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import seaborn as sns

plt.rcParams.update({
    "figure.dpi": 300,
    "axes.titlesize": 14,
    "axes.labelsize": 12,
    "legend.fontsize": 10,
    "font.family": "serif",
})

EXCEL_DEFAULT = "solar_model.xlsx"
FIG_DIR = Path("figures")
FIG_DIR.mkdir(exist_ok=True)


# ---------------------------------------------------------------------------
# Chargement des données
# ---------------------------------------------------------------------------

def load_data(path: Path):
    xls = pd.ExcelFile(path)
    cash = pd.read_excel(xls, sheet_name="Cashflow")
    kpis = pd.read_excel(xls, sheet_name="KPIs")
    sens = pd.read_excel(xls, sheet_name="Sensitivity")

    # Nettoyage rapide : renommer la colonne index issue de pandas
    if cash.columns[0].startswith("Unnamed"):  # Années
        cash = cash.rename(columns={cash.columns[0]: "Année"})

    # Assurer qu'une colonne "Année" existe même si le nom est différent
    if "Année" not in cash.columns:
        first_col = cash.columns[0]
        cash = cash.rename(columns={first_col: "Année"})

    # S'assurer que la colonne Année est numérique (utile pour l'axe x)
    cash["Année"] = pd.to_numeric(cash["Année"], errors="coerce")

    return cash, kpis, sens


# ---------------------------------------------------------------------------
# Graphiques
# ---------------------------------------------------------------------------

def fcfe_cumule(cash: pd.DataFrame):
    if "Equity_CF" not in cash.columns:
        print("Colonne 'Equity_CF' introuvable dans Cashflow – graphique 1 ignoré")
        return
    cash["FCFE_cumule"] = cash["Equity_CF"].cumsum()
    plt.figure(figsize=(8, 5))
    sns.lineplot(data=cash, x="Année", y="FCFE_cumule", marker="o", color="royalblue")
    plt.title("Flux de trésorerie disponibles pour les actionnaires – cumul")
    plt.xlabel("Année")
    plt.ylabel("FCFE cumulé (EUR)")
    plt.tight_layout()
    out = FIG_DIR / "fcfe_cumule.png"
    plt.savefig(out)
    plt.close()
    print("Graphique enregistré :", out)


def dscr_plot(cash: pd.DataFrame):
    if "DSCR" not in cash.columns:
        print("Colonne 'DSCR' introuvable – graphique 2 ignoré")
        return
    plt.figure(figsize=(8, 5))
    sns.lineplot(data=cash, x="Année", y="DSCR", marker="s", color="darkgreen")
    plt.axhline(1.20, color="red", linestyle="--", label="Covenant 1,20")
    plt.ylim(0, max(2, cash["DSCR"].max() * 1.1))
    plt.title("Couverture du service de la dette (DSCR)")
    plt.xlabel("Année")
    plt.ylabel("DSCR")
    plt.legend()
    plt.tight_layout()
    out = FIG_DIR / "dscr.png"
    plt.savefig(out)
    plt.close()
    print("Graphique enregistré :", out)


def sens_tri(sens: pd.DataFrame):
    if sens.empty:
        print("Feuille Sensitivity vide – graphique 3 ignoré")
        return
    # Trier par impact absolu (delta vs base)
    sens_sorted = sens.copy()
    if "Δ vs base" in sens.columns:
        sens_sorted["Impact"] = sens["Δ vs base"].abs()
        sens_sorted = sens_sorted.sort_values("Impact", ascending=True)
    plt.figure(figsize=(8, 5))
    sns.barplot(y="Scenario", x="IRR_equity", data=sens_sorted, color="steelblue")
    plt.xlabel("TRI sur capitaux propres")
    plt.ylabel("Scénario de variation")
    plt.title("Analyse de sensibilité du TRI")
    plt.tight_layout()
    out = FIG_DIR / "sens_tri.png"
    plt.savefig(out)
    plt.close()
    print("Graphique enregistré :", out)


def structure_financement_pie(cash: pd.DataFrame):
    # Année 0 : calcul des flux initiaux
    row0 = cash.loc[0]
    capex = -row0["Capex"]
    grants = row0["Grants"]
    
    # Vérifier si Debt_Draw existe, sinon calculer à partir du ratio dette
    if "Debt_Draw" in cash.columns:
        debt = -row0["Debt_Draw"]
    elif "Principal" in cash.columns:
        # Calculer la dette comme la somme de tous les remboursements de principal
        debt = -cash["Principal"].sum()
    else:
        # Fallback: 60% du montant après subventions
        debt = (capex - grants) * 0.60
        
    equity = capex - debt - grants

    labels = ["Subventions", "Dette", "Capitaux propres"]
    sizes = [grants, debt, equity]
    colors = ["#4daf4a", "#377eb8", "#e41a1c"]

    plt.figure(figsize=(6, 6))
    plt.pie(sizes, labels=labels, autopct="%.1f%%", colors=colors, startangle=90)
    plt.title("Structure initiale de financement")
    plt.tight_layout()
    out = FIG_DIR / "structure_financement.png"
    plt.savefig(out)
    plt.close()
    print("Graphique enregistré :", out)


# ---------------------------------------------------------------------------
# Main
# ---------------------------------------------------------------------------

def scenario_comparison():
    """Crée un graphique comparant les scénarios des deux sites et l'impact des subventions MASEN/IRESEN"""
    # Définir les scénarios et leurs résultats (valeurs issues du script financial_model.py)
    scenarios = [
        "Ouarzazate",
        "Dakhla",
        "Ouarzazate\n(stress)",
        "Ouarzazate\n(stress sans\nMASEN/IRESEN)"
    ]
    
    tri_values = [29.3, 30.0, 17.7, 12.6]
    colors = ['#2ca02c', '#2ca02c', '#ff7f0e', '#e74c3c']
    
    # Créer le graphique
    plt.figure(figsize=(10, 6))
    bars = plt.bar(scenarios, tri_values, color=colors)
    
    # Ajouter une ligne horizontale pour le seuil de viabilité
    plt.axhline(y=10.0, color='red', linestyle='--', alpha=0.7, label='Seuil de viabilité (10%)')
    
    # Annotations et formatage
    plt.ylabel('TRI sur capitaux propres (%)')
    plt.title('Comparaison des TRI par scénario et impact des subventions MASEN/IRESEN')
    
    # Ajouter les valeurs sur les barres
    for i, bar in enumerate(bars):
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                f'{tri_values[i]}%', ha='center', va='bottom', fontweight='bold')
    
    # Ajouter flèche et annotation pour montrer l'impact des subventions MASEN/IRESEN
    plt.annotate('Impact MASEN/IRESEN:\n+5.1 points de TRI', 
                xy=(3, 12.6), xytext=(3.2, 15),
                arrowprops=dict(arrowstyle='->', color='black', lw=1.5),
                fontsize=10, ha='center')
    
    plt.legend()
    plt.tight_layout()
    
    out = FIG_DIR / "scenario_comparison.png"
    plt.savefig(out)
    plt.close()
    print("Graphique enregistré :", out)

def main(excel_path: str | None):
    file_path = Path(excel_path) if excel_path else Path(EXCEL_DEFAULT)
    if not file_path.exists():
        print(f"Fichier Excel '{file_path}' introuvable – interrompu.")
        sys.exit(1)

    cash, kpis, sens = load_data(file_path)

    fcfe_cumule(cash)
    dscr_plot(cash)
    sens_tri(sens)
    structure_financement_pie(cash)
    scenario_comparison()  # Nouvelle fonction pour comparer les scénarios

    print("Tous les graphiques générés dans le dossier 'figures'.")


if __name__ == "__main__":
    arg = sys.argv[1] if len(sys.argv) > 1 else None
    main(arg)
