#!/usr/bin/env python3
"""
Simple test for DOCX functionality
"""

def test_docx_basic():
    """Test basic DOCX functionality"""
    print("🔬 Testing DOCX functionality...")
    
    try:
        # Test import
        print("1. Testing import...")
        from docx import Document
        from docx.shared import Inches, Pt
        from docx.enum.text import WD_ALIGN_PARAGRAPH
        from docx.enum.table import WD_TABLE_ALIGNMENT
        print("   ✅ Import successful")
        
        # Test document creation
        print("2. Testing document creation...")
        doc = Document()
        print("   ✅ Document created")
        
        # Test adding content
        print("3. Testing content addition...")
        doc.add_heading('Test Report', 0)
        doc.add_paragraph('This is a test paragraph.')
        print("   ✅ Content added")
        
        # Test signature section (like in main app)
        print("4. Testing signature section...")
        doc.add_heading('Report Prepared By', level=2)
        
        # Create signature table
        sig_table = doc.add_table(rows=1, cols=1)
        sig_table.style = 'Table Grid'
        sig_table.alignment = WD_TABLE_ALIGNMENT.CENTER
        
        # Add signature content
        sig_cell = sig_table.rows[0].cells[0]
        sig_para = sig_cell.paragraphs[0]
        sig_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # Add name
        name_run = sig_para.add_run('Abdelhalim Serhani')
        name_run.font.size = Pt(16)
        name_run.font.bold = True
        
        sig_para.add_run('\n')
        
        # Add title
        title_run = sig_para.add_run('Business & Financial Consulting')
        title_run.font.size = Pt(12)
        title_run.font.italic = True
        
        sig_para.add_run('\n')
        
        # Add company
        company_run = sig_para.add_run('@ ')
        company_run.font.size = Pt(12)
        
        agevolami_run = sig_para.add_run('Agevolami.it')
        agevolami_run.font.size = Pt(12)
        agevolami_run.font.bold = True
        agevolami_run.font.underline = True
        
        sig_para.add_run('\n\n')
        
        # Add tagline
        tagline_run = sig_para.add_run('🌟 Empowering Renewable Energy Investments in Morocco & Beyond 🌟')
        tagline_run.font.size = Pt(10)
        tagline_run.font.italic = True
        
        print("   ✅ Signature section added")
        
        # Test saving
        print("5. Testing file save...")
        filename = 'test_docx_simple.docx'
        doc.save(filename)
        print(f"   ✅ File saved: {filename}")
        
        # Check if file exists
        from pathlib import Path
        file_path = Path(filename)
        if file_path.exists():
            file_size = file_path.stat().st_size
            print(f"   ✅ File verified: {file_size} bytes")
            print(f"   📁 Location: {file_path.absolute()}")
            return True
        else:
            print("   ❌ File not found after save!")
            return False
            
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        print("Install with: pip install python-docx")
        return False
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔬 Simple DOCX Test")
    print("=" * 30)
    
    success = test_docx_basic()
    
    if success:
        print("\n🎉 DOCX functionality is working!")
        print("✅ The main app should be able to export DOCX files")
    else:
        print("\n🚨 DOCX functionality has issues")
        print("❌ Check the error messages above")
