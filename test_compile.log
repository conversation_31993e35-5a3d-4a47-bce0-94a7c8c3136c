This is pdfTeX, Version 3.141592653-2.6-1.40.25 (MiKTeX 24.1) (preloaded format=pdflatex 2025.1.20)  8 JUN 2025 23:19
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**./test_compile.tex
(test_compile.tex
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-01-04>
(D:\miktex\tex/latex/base\report.cls
Document Class: report 2023/05/17 v1.4n Standard LaTeX document class
(D:\miktex\tex/latex/base\size12.clo
File: size12.clo 2023/05/17 v1.4n Standard LaTeX file (size option)
)
\c@part=\count187
\c@chapter=\count188
\c@section=\count189
\c@subsection=\count190
\c@subsubsection=\count191
\c@paragraph=\count192
\c@subparagraph=\count193
\c@figure=\count194
\c@table=\count195
\abovecaptionskip=\skip48
\belowcaptionskip=\skip49
\bibindent=\dimen140
) (D:\miktex\tex/latex/base\inputenc.sty
Package: inputenc 2021/02/14 v1.3d Input encoding file
\inpenc@prehook=\toks17
\inpenc@posthook=\toks18
)
(D:\miktex\tex/latex/base\fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
) (D:\miktex\tex/generic/babel\babel.sty
Package: babel 2024/01/07 v24.1 The Babel package
\babel@savecnt=\count196
\U@D=\dimen141
\l@unhyphenated=\language79

(D:\miktex\tex/generic/babel\txtbabel.def)
\bbl@readstream=\read2
\bbl@dirlevel=\count197

*************************************
* Local config file bblopts.cfg used
*
(D:\miktex\tex/latex/arabi\bblopts.cfg
File: bblopts.cfg 2005/09/08 v0.1 add Arabic and Farsi to "declared" options of
 babel
)
(D:\miktex\tex/generic/babel-french\french.ldf
Language: french 2024-07-25 v3.6c French support from the babel system
Package babel Info: Hyphen rules for 'acadian' set to \l@french
(babel)             (\language22). Reported on input line 91.
Package babel Info: Hyphen rules for 'canadien' set to \l@french
(babel)             (\language22). Reported on input line 92.
\FB@stdchar=\count198
Package babel Info: Making : an active character on input line 421.
Package babel Info: Making ; an active character on input line 422.
Package babel Info: Making ! an active character on input line 423.
Package babel Info: Making ? an active character on input line 424.
\FBguill@level=\count199
\FBold@everypar=\toks19
\FB@Mht=\dimen142
\mc@charclass=\count266
\mc@charfam=\count267
\mc@charslot=\count268
\std@mcc=\count269
\dec@mcc=\count270
\FB@parskip=\dimen143
\listindentFB=\dimen144
\descindentFB=\dimen145
\labelindentFB=\dimen146
\labelwidthFB=\dimen147
\leftmarginFB=\dimen148
\parindentFFN=\dimen149
\FBfnindent=\dimen150
))
(D:\miktex\tex/generic/babel/locale/fr\babel-french.tex
Package babel Info: Importing font and identification data for french
(babel)             from babel-fr.ini. Reported on input line 11.
)
(D:\miktex\tex/latex/carlisle\scalefnt.sty)
(D:\miktex\tex/latex/graphics\graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)

(D:\miktex\tex/latex/graphics\keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks20
)
(D:\miktex\tex/latex/graphics\graphics.sty
Package: graphics 2022/03/10 v1.4e Standard LaTeX Graphics (DPC,SPQR)

(D:\miktex\tex/latex/graphics\trig.sty
Package: trig 2021/08/11 v1.11 sin cos tan (DPC)
)
(D:\miktex\tex/latex/graphics-cfg\graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 107.

(D:\miktex\tex/latex/graphics-def\pdftex.def
File: pdftex.def 2022/09/22 v1.2b Graphics/color driver for pdftex
))
\Gin@req@height=\dimen151
\Gin@req@width=\dimen152
)
(D:\miktex\tex/latex/hyperref\hyperref.sty
Package: hyperref 2023-11-26 v7.01g Hypertext links for LaTeX

(D:\miktex\tex/generic/iftex\iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
)
(D:\miktex\tex/generic/infwarerr\infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
(D:\miktex\tex/latex/kvsetkeys\kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
)
(D:\miktex\tex/generic/kvdefinekeys\kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
)
(D:\miktex\tex/generic/pdfescape\pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)

(D:\miktex\tex/generic/ltxcmds\ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
)
(D:\miktex\tex/generic/pdftexcmds\pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
))
(D:\miktex\tex/latex/hycolor\hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
)
(D:\miktex\tex/latex/letltxmacro\letltxmacro.sty
Package: letltxmacro 2019/12/03 v1.6 Let assignment for LaTeX macros (HO)
)
(D:\miktex\tex/latex/auxhook\auxhook.sty
Package: auxhook 2019-12-17 v1.6 Hooks for auxiliary files (HO)
)
(D:\miktex\tex/latex/hyperref\nameref.sty
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section

(D:\miktex\tex/latex/refcount\refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
)
(D:\miktex\tex/generic/gettitlestring\gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)

(D:\miktex\tex/latex/kvoptions\kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
))
\c@section@level=\count271
)
(D:\miktex\tex/latex/etoolbox\etoolbox.sty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count272
)
\@linkdim=\dimen153
\Hy@linkcounter=\count273
\Hy@pagecounter=\count274

(D:\miktex\tex/latex/hyperref\pd1enc.def
File: pd1enc.def 2023-11-26 v7.01g Hyperref: PDFDocEncoding definition (HO)
Now handling font encoding PD1 ...
... no UTF-8 mapping file for font encoding PD1
)
(D:\miktex\tex/generic/intcalc\intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count275

(D:\miktex\tex/latex/hyperref\puenc.def
File: puenc.def 2023-11-26 v7.01g Hyperref: PDF Unicode definition (HO)
Now handling font encoding PU ...
... no UTF-8 mapping file for font encoding PU
)
Package hyperref Info: Hyper figures OFF on input line 4181.
Package hyperref Info: Link nesting OFF on input line 4186.
Package hyperref Info: Hyper index ON on input line 4189.
Package hyperref Info: Plain pages OFF on input line 4196.
Package hyperref Info: Backreferencing OFF on input line 4201.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4448.
\c@Hy@tempcnt=\count276
 (D:\miktex\tex/latex/url\url.sty
\Urlmuskip=\muskip16
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4786.
\XeTeXLinkMargin=\dimen154

(D:\miktex\tex/generic/bitset\bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)

(D:\miktex\tex/generic/bigintcalc\bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO
)
))
\Fld@menulength=\count277
\Field@Width=\dimen155
\Fld@charsize=\dimen156
Package hyperref Info: Hyper figures OFF on input line 6065.
Package hyperref Info: Link nesting OFF on input line 6070.
Package hyperref Info: Hyper index ON on input line 6073.
Package hyperref Info: backreferencing OFF on input line 6080.
Package hyperref Info: Link coloring OFF on input line 6085.
Package hyperref Info: Link coloring with OCG OFF on input line 6090.
Package hyperref Info: PDF/A mode OFF on input line 6095.

(D:\miktex\tex/latex/base\atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count278
\c@Item=\count279
\c@Hfootnote=\count280
)
Package hyperref Info: Driver (autodetected): hpdftex.

(D:\miktex\tex/latex/hyperref\hpdftex.def
File: hpdftex.def 2023-11-26 v7.01g Hyperref driver for pdfTeX

(D:\miktex\tex/latex/base\atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend pac
kage
with kernel methods
)
\Fld@listcount=\count281
\c@bookmark@seq@number=\count282

(D:\miktex\tex/latex/rerunfilecheck\rerunfilecheck.sty
Package: rerunfilecheck 2022-07-10 v1.10 Rerun checks for auxiliary files (HO)

(D:\miktex\tex/generic/uniquecounter\uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 2
85.
)
\Hy@SectionHShift=\skip50
)
(D:\miktex\tex/latex/setspace\setspace.sty
Package: setspace 2022/12/04 v6.7b set line spacing
)
(D:\miktex\tex/latex/titlesec\titlesec.sty
Package: titlesec 2025/01/04 v2.17 Sectioning titles
\ttl@box=\box51
\beforetitleunit=\skip51
\aftertitleunit=\skip52
\ttl@plus=\dimen157
\ttl@minus=\dimen158
\ttl@toksa=\toks21
\titlewidth=\dimen159
\titlewidthlast=\dimen160
\titlewidthfirst=\dimen161
)
(D:\miktex\tex/latex/tocloft\tocloft.sty
Package: tocloft 2017/08/31 v2.3i parameterised ToC, etc., typesetting
Package tocloft Info: The document has chapter divisions on input line 51.
\cftparskip=\skip53
\cftbeforetoctitleskip=\skip54
\cftaftertoctitleskip=\skip55
\cftbeforepartskip=\skip56
\cftpartnumwidth=\skip57
\cftpartindent=\skip58
\cftbeforechapskip=\skip59
\cftchapindent=\skip60
\cftchapnumwidth=\skip61
\cftbeforesecskip=\skip62
\cftsecindent=\skip63
\cftsecnumwidth=\skip64
\cftbeforesubsecskip=\skip65
\cftsubsecindent=\skip66
\cftsubsecnumwidth=\skip67
\cftbeforesubsubsecskip=\skip68
\cftsubsubsecindent=\skip69
\cftsubsubsecnumwidth=\skip70
\cftbeforeparaskip=\skip71
\cftparaindent=\skip72
\cftparanumwidth=\skip73
\cftbeforesubparaskip=\skip74
\cftsubparaindent=\skip75
\cftsubparanumwidth=\skip76
\cftbeforeloftitleskip=\skip77
\cftafterloftitleskip=\skip78
\cftbeforefigskip=\skip79
\cftfigindent=\skip80
\cftfignumwidth=\skip81
\c@lofdepth=\count283
\c@lotdepth=\count284
\cftbeforelottitleskip=\skip82
\cftafterlottitleskip=\skip83
\cftbeforetabskip=\skip84
\cfttabindent=\skip85
\cfttabnumwidth=\skip86
)
(D:\miktex\tex/latex/tocbibind\tocbibind.sty
Package: tocbibind 2010/10/13 v1.5k extra ToC listings
Package tocbibind Info: The document has chapter divisions on input line 50.


Package tocbibind Note: Using chapter style headings, unless overridden.

) (D:\miktex\tex/latex/fancyhdr\fancyhdr.sty
Package: fancyhdr 2025/01/07 v5.1.1 Extensive control of page headers and foote
rs
\f@nch@headwidth=\skip87
\f@nch@offset@elh=\skip88
\f@nch@offset@erh=\skip89
\f@nch@offset@olh=\skip90
\f@nch@offset@orh=\skip91
\f@nch@offset@elf=\skip92
\f@nch@offset@erf=\skip93
\f@nch@offset@olf=\skip94
\f@nch@offset@orf=\skip95
\f@nch@height=\skip96
\f@nch@footalignment=\skip97
\f@nch@widthL=\skip98
\f@nch@widthC=\skip99
\f@nch@widthR=\skip100
\@temptokenb=\toks22
)
(D:\miktex\tex/latex/geometry\geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry

(D:\miktex\tex/generic/iftex\ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.
)
\Gm@cnth=\count285
\Gm@cntv=\count286
\c@Gm@tempcnt=\count287
\Gm@bindingoffset=\dimen162
\Gm@wd@mp=\dimen163
\Gm@odd@mp=\dimen164
\Gm@even@mp=\dimen165
\Gm@layoutwidth=\dimen166
\Gm@layoutheight=\dimen167
\Gm@layouthoffset=\dimen168
\Gm@layoutvoffset=\dimen169
\Gm@dimlist=\toks23

(D:\miktex\tex/latex/geometry\geometry.cfg))
(D:\miktex\tex/latex/tools\indentfirst.sty
Package: indentfirst 2023/07/02 v1.03 Indent first paragraph (DPC)
)
(D:\miktex\tex/latex/natbib\natbib.sty
Package: natbib 2010/09/13 8.31b (PWD, AO)
\bibhang=\skip101
\bibsep=\skip102
LaTeX Info: Redefining \cite on input line 694.
\c@NAT@ctr=\count288
) (D:\miktex\tex/latex/xcolor\xcolor.sty
Package: xcolor 2023/11/15 v3.01 LaTeX color extensions (UK)

(D:\miktex\tex/latex/graphics-cfg\color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.

(D:\miktex\tex/latex/graphics\mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1350.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1354.
Package xcolor Info: Model `RGB' extended on input line 1366.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1368.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1371.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1372.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1373.
)
(D:\miktex\tex/latex/appendix\appendix.sty
Package: appendix 2020/02/08 v1.2c extra appendix facilities
\c@@pps=\count289
\c@@ppsavesec=\count290
\c@@ppsaveapp=\count291
)
(D:\miktex\tex/latex/minitoc\minitoc.sty
Package: minitoc 2018/07/12 v62 Package minitoc

(D:\miktex\tex/latex/minitoc\mtcmess.sty
Package: mtcmess 2006/03/14
)
Package minitoc Info: I0001
(minitoc)             *** minitoc package, version 62 ***.
\tf@mtc=\write3
\mtcindent=\skip103
\mtcskipamount=\skip104
Package minitoc Info: I0005
(minitoc)             compatible with hyperref.
Package minitoc Info: I0023
(minitoc)             part level macros available.
Package minitoc Info: I0003
(minitoc)             chapter level macros available.
\mtc@toks=\toks24
\mtc@strutbox=\box52
\mtc@hstrutbox=\box53
Package minitoc Info: I0002
(minitoc)             Autoconfiguration of extensions.
\openout3 = `test_compile.mtc0'.

\openout3 = `test_compile.mtc'.

 (test_compile.mtc0)
Package minitoc Info: I0012
(minitoc)             Long extensions (Unix-like) will be used.
Package minitoc Info: I0031
(minitoc)             ==> this version is configured for UNIX-like 
(minitoc)                 (long extensions) file names.
\openout3 = `test_compile.mtc'.

\openout3 = `test_compile.mtc0'.

\c@mtc=\count292
\c@minitocdepth=\count293
\c@ptc=\count294
\c@parttocdepth=\count295
\ptcindent=\skip105
Package minitoc Info: I0010
(minitoc)             The english language is selected.
(minitoc)              on input line 4910.

(D:\miktex\tex/latex/minitoc\english.mld
File: english.mld 2006/01/13
)
(D:\miktex\tex/latex/minitoc\english.mld
File: english.mld 2006/01/13
)) (D:\miktex\tex/latex/etoc\etoc.sty
Package: etoc 2023/10/29 v1.2d Completely customisable TOCs (JFB)

(D:\miktex\tex/latex/tools\multicol.sty
Package: multicol 2023/03/30 v1.9f multicolumn formatting (FMi)
\c@tracingmulticols=\count296
\mult@box=\box54
\multicol@leftmargin=\dimen170
\c@unbalance=\count297
\c@collectmore=\count298
\doublecol@number=\count299
\multicoltolerance=\count300
\multicolpretolerance=\count301
\full@width=\dimen171
\page@free=\dimen172
\premulticols=\dimen173
\postmulticols=\dimen174
\multicolsep=\skip106
\multicolbaselineskip=\skip107
\partial@page=\box55
\last@line=\box56
\mc@boxedresult=\box57
\maxbalancingoverflow=\dimen175
\mult@rightbox=\box58
\mult@grightbox=\box59
\mult@firstbox=\box60
\mult@gfirstbox=\box61
\@tempa=\box62
\@tempa=\box63
\@tempa=\box64
\@tempa=\box65
\@tempa=\box66
\@tempa=\box67
\@tempa=\box68
\@tempa=\box69
\@tempa=\box70
\@tempa=\box71
\@tempa=\box72
\@tempa=\box73
\@tempa=\box74
\@tempa=\box75
\@tempa=\box76
\@tempa=\box77
\@tempa=\box78
\@tempa=\box79
\@tempa=\box80
\@tempa=\box81
\@tempa=\box82
\@tempa=\box83
\@tempa=\box84
\@tempa=\box85
\@tempa=\box86
\@tempa=\box87
\@tempa=\box88
\@tempa=\box89
\@tempa=\box90
\@tempa=\box91
\@tempa=\box92
\@tempa=\box93
\@tempa=\box94
\@tempa=\box95
\@tempa=\box96
\@tempa=\box97
\c@minrows=\count302
\c@columnbadness=\count303
\c@finalcolumnbadness=\count304
\last@try=\dimen176
\multicolovershoot=\dimen177
\multicolundershoot=\dimen178
\mult@nat@firstbox=\box98
\colbreak@box=\box99
\mc@col@check@num=\count305
)
\Etoc@toctoks=\toks25
\c@etoc@tocid=\count306
\etoc@framed@titlebox=\box100
\etoc@framed@contentsbox=\box101
)
(D:\miktex\tex/latex/pgf/frontendlayer\tikz.sty
(D:\miktex\tex/latex/pgf/basiclayer\pgf.sty
(D:\miktex\tex/latex/pgf/utilities\pgfrcs.sty
(D:\miktex\tex/generic/pgf/utilities\pgfutil-common.tex
\pgfutil@everybye=\toks26
\pgfutil@tempdima=\dimen179
\pgfutil@tempdimb=\dimen180
)
(D:\miktex\tex/generic/pgf/utilities\pgfutil-latex.def
\pgfutil@abb=\box102
)
(D:\miktex\tex/generic/pgf/utilities\pgfrcs.code.tex
(D:\miktex\tex/generic/pgf\pgf.revision.tex)
Package: pgfrcs 2023-01-15 v3.1.10 (3.1.10)
))
Package: pgf 2023-01-15 v3.1.10 (3.1.10)

(D:\miktex\tex/latex/pgf/basiclayer\pgfcore.sty
(D:\miktex\tex/latex/pgf/systemlayer\pgfsys.sty
(D:\miktex\tex/generic/pgf/systemlayer\pgfsys.code.tex
Package: pgfsys 2023-01-15 v3.1.10 (3.1.10)

(D:\miktex\tex/generic/pgf/utilities\pgfkeys.code.tex
\pgfkeys@pathtoks=\toks27
\pgfkeys@temptoks=\toks28

(D:\miktex\tex/generic/pgf/utilities\pgfkeyslibraryfiltered.code.tex
\pgfkeys@tmptoks=\toks29
))
\pgf@x=\dimen181
\pgf@y=\dimen182
\pgf@xa=\dimen183
\pgf@ya=\dimen184
\pgf@xb=\dimen185
\pgf@yb=\dimen186
\pgf@xc=\dimen187
\pgf@yc=\dimen188
\pgf@xd=\dimen189
\pgf@yd=\dimen190
\w@pgf@writea=\write4
\r@pgf@reada=\read3
\c@pgf@counta=\count307
\c@pgf@countb=\count308
\c@pgf@countc=\count309
\c@pgf@countd=\count310
\t@pgf@toka=\toks30
\t@pgf@tokb=\toks31
\t@pgf@tokc=\toks32
\pgf@sys@id@count=\count311

(D:\miktex\tex/generic/pgf/systemlayer\pgf.cfg
File: pgf.cfg 2023-01-15 v3.1.10 (3.1.10)
)
Driver file for pgf: pgfsys-pdftex.def

(D:\miktex\tex/generic/pgf/systemlayer\pgfsys-pdftex.def
File: pgfsys-pdftex.def 2023-01-15 v3.1.10 (3.1.10)

(D:\miktex\tex/generic/pgf/systemlayer\pgfsys-common-pdf.def
File: pgfsys-common-pdf.def 2023-01-15 v3.1.10 (3.1.10)
)))
(D:\miktex\tex/generic/pgf/systemlayer\pgfsyssoftpath.code.tex
File: pgfsyssoftpath.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfsyssoftpath@smallbuffer@items=\count312
\pgfsyssoftpath@bigbuffer@items=\count313
)
(D:\miktex\tex/generic/pgf/systemlayer\pgfsysprotocol.code.tex
File: pgfsysprotocol.code.tex 2023-01-15 v3.1.10 (3.1.10)
))
(D:\miktex\tex/generic/pgf/basiclayer\pgfcore.code.tex
Package: pgfcore 2023-01-15 v3.1.10 (3.1.10)

(D:\miktex\tex/generic/pgf/math\pgfmath.code.tex
(D:\miktex\tex/generic/pgf/math\pgfmathutil.code.tex)
(D:\miktex\tex/generic/pgf/math\pgfmathparser.code.tex
\pgfmath@dimen=\dimen191
\pgfmath@count=\count314
\pgfmath@box=\box103
\pgfmath@toks=\toks33
\pgfmath@stack@operand=\toks34
\pgfmath@stack@operation=\toks35
)
(D:\miktex\tex/generic/pgf/math\pgfmathfunctions.code.tex)
(D:\miktex\tex/generic/pgf/math\pgfmathfunctions.basic.code.tex)
(D:\miktex\tex/generic/pgf/math\pgfmathfunctions.trigonometric.code.tex)
(D:\miktex\tex/generic/pgf/math\pgfmathfunctions.random.code.tex)
(D:\miktex\tex/generic/pgf/math\pgfmathfunctions.comparison.code.tex)
(D:\miktex\tex/generic/pgf/math\pgfmathfunctions.base.code.tex)
(D:\miktex\tex/generic/pgf/math\pgfmathfunctions.round.code.tex)
(D:\miktex\tex/generic/pgf/math\pgfmathfunctions.misc.code.tex)
(D:\miktex\tex/generic/pgf/math\pgfmathfunctions.integerarithmetics.code.tex)
(D:\miktex\tex/generic/pgf/math\pgfmathcalc.code.tex)
(D:\miktex\tex/generic/pgf/math\pgfmathfloat.code.tex
\c@pgfmathroundto@lastzeros=\count315
))
(D:\miktex\tex/generic/pgf/math\pgfint.code.tex)
(D:\miktex\tex/generic/pgf/basiclayer\pgfcorepoints.code.tex
File: pgfcorepoints.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@picminx=\dimen192
\pgf@picmaxx=\dimen193
\pgf@picminy=\dimen194
\pgf@picmaxy=\dimen195
\pgf@pathminx=\dimen196
\pgf@pathmaxx=\dimen197
\pgf@pathminy=\dimen198
\pgf@pathmaxy=\dimen199
\pgf@xx=\dimen256
\pgf@xy=\dimen257
\pgf@yx=\dimen258
\pgf@yy=\dimen259
\pgf@zx=\dimen260
\pgf@zy=\dimen261
)
(D:\miktex\tex/generic/pgf/basiclayer\pgfcorepathconstruct.code.tex
File: pgfcorepathconstruct.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@path@lastx=\dimen262
\pgf@path@lasty=\dimen263
)
(D:\miktex\tex/generic/pgf/basiclayer\pgfcorepathusage.code.tex
File: pgfcorepathusage.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@shorten@end@additional=\dimen264
\pgf@shorten@start@additional=\dimen265
)
(D:\miktex\tex/generic/pgf/basiclayer\pgfcorescopes.code.tex
File: pgfcorescopes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfpic=\box104
\pgf@hbox=\box105
\pgf@layerbox@main=\box106
\pgf@picture@serial@count=\count316
)
(D:\miktex\tex/generic/pgf/basiclayer\pgfcoregraphicstate.code.tex
File: pgfcoregraphicstate.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgflinewidth=\dimen266
)
(D:\miktex\tex/generic/pgf/basiclayer\pgfcoretransformations.code.tex
File: pgfcoretransformations.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@pt@x=\dimen267
\pgf@pt@y=\dimen268
\pgf@pt@temp=\dimen269
)
(D:\miktex\tex/generic/pgf/basiclayer\pgfcorequick.code.tex
File: pgfcorequick.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(D:\miktex\tex/generic/pgf/basiclayer\pgfcoreobjects.code.tex
File: pgfcoreobjects.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(D:\miktex\tex/generic/pgf/basiclayer\pgfcorepathprocessing.code.tex
File: pgfcorepathprocessing.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(D:\miktex\tex/generic/pgf/basiclayer\pgfcorearrows.code.tex
File: pgfcorearrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfarrowsep=\dimen270
)
(D:\miktex\tex/generic/pgf/basiclayer\pgfcoreshade.code.tex
File: pgfcoreshade.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@max=\dimen271
\pgf@sys@shading@range@num=\count317
\pgf@shadingcount=\count318
)
(D:\miktex\tex/generic/pgf/basiclayer\pgfcoreimage.code.tex
File: pgfcoreimage.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(D:\miktex\tex/generic/pgf/basiclayer\pgfcoreexternal.code.tex
File: pgfcoreexternal.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfexternal@startupbox=\box107
)
(D:\miktex\tex/generic/pgf/basiclayer\pgfcorelayers.code.tex
File: pgfcorelayers.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(D:\miktex\tex/generic/pgf/basiclayer\pgfcoretransparency.code.tex
File: pgfcoretransparency.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(D:\miktex\tex/generic/pgf/basiclayer\pgfcorepatterns.code.tex
File: pgfcorepatterns.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(D:\miktex\tex/generic/pgf/basiclayer\pgfcorerdf.code.tex
File: pgfcorerdf.code.tex 2023-01-15 v3.1.10 (3.1.10)
)))
(D:\miktex\tex/generic/pgf/modules\pgfmoduleshapes.code.tex
File: pgfmoduleshapes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfnodeparttextbox=\box108
)
(D:\miktex\tex/generic/pgf/modules\pgfmoduleplot.code.tex
File: pgfmoduleplot.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(D:\miktex\tex/latex/pgf/compatibility\pgfcomp-version-0-65.sty
Package: pgfcomp-version-0-65 2023-01-15 v3.1.10 (3.1.10)
\pgf@nodesepstart=\dimen272
\pgf@nodesepend=\dimen273
)
(D:\miktex\tex/latex/pgf/compatibility\pgfcomp-version-1-18.sty
Package: pgfcomp-version-1-18 2023-01-15 v3.1.10 (3.1.10)
))
(D:\miktex\tex/latex/pgf/utilities\pgffor.sty
(D:\miktex\tex/latex/pgf/utilities\pgfkeys.sty
(D:\miktex\tex/generic/pgf/utilities\pgfkeys.code.tex))
(D:\miktex\tex/latex/pgf/math\pgfmath.sty
(D:\miktex\tex/generic/pgf/math\pgfmath.code.tex))
(D:\miktex\tex/generic/pgf/utilities\pgffor.code.tex
Package: pgffor 2023-01-15 v3.1.10 (3.1.10)
\pgffor@iter=\dimen274
\pgffor@skip=\dimen275
\pgffor@stack=\toks36
\pgffor@toks=\toks37
))
(D:\miktex\tex/generic/pgf/frontendlayer/tikz\tikz.code.tex
Package: tikz 2023-01-15 v3.1.10 (3.1.10)

(D:\miktex\tex/generic/pgf/libraries\pgflibraryplothandlers.code.tex
File: pgflibraryplothandlers.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@plot@mark@count=\count319
\pgfplotmarksize=\dimen276
)
\tikz@lastx=\dimen277
\tikz@lasty=\dimen278
\tikz@lastxsaved=\dimen279
\tikz@lastysaved=\dimen280
\tikz@lastmovetox=\dimen281
\tikz@lastmovetoy=\dimen282
\tikzleveldistance=\dimen283
\tikzsiblingdistance=\dimen284
\tikz@figbox=\box109
\tikz@figbox@bg=\box110
\tikz@tempbox=\box111
\tikz@tempbox@bg=\box112
\tikztreelevel=\count320
\tikznumberofchildren=\count321
\tikznumberofcurrentchild=\count322
\tikz@fig@count=\count323

(D:\miktex\tex/generic/pgf/modules\pgfmodulematrix.code.tex
File: pgfmodulematrix.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfmatrixcurrentrow=\count324
\pgfmatrixcurrentcolumn=\count325
\pgf@matrix@numberofcolumns=\count326
)
\tikz@expandcount=\count327

(D:\miktex\tex/generic/pgf/frontendlayer/tikz/libraries\tikzlibrarytopaths.code
.tex
File: tikzlibrarytopaths.code.tex 2023-01-15 v3.1.10 (3.1.10)
)))
(D:\miktex\tex/generic/pgf/frontendlayer/tikz/libraries\tikzlibraryshapes.geome
tric.code.tex
File: tikzlibraryshapes.geometric.code.tex 2023-01-15 v3.1.10 (3.1.10)

(D:\miktex\tex/generic/pgf/libraries/shapes\pgflibraryshapes.geometric.code.tex
File: pgflibraryshapes.geometric.code.tex 2023-01-15 v3.1.10 (3.1.10)
))
(D:\miktex\tex/generic/pgf/frontendlayer/tikz/libraries\tikzlibrarypositioning.
code.tex
File: tikzlibrarypositioning.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (D:\miktex\tex/generic/pgf/libraries\pgflibraryarrows.meta.code.tex
File: pgflibraryarrows.meta.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfarrowinset=\dimen285
\pgfarrowlength=\dimen286
\pgfarrowwidth=\dimen287
\pgfarrowlinewidth=\dimen288
)
(D:\miktex\tex/latex/tools\array.sty
Package: array 2023/10/16 v2.5g Tabular extension package (FMi)
\col@sep=\dimen289
\ar@mcellbox=\box113
\extrarowheight=\dimen290
\NC@list=\toks38
\extratabsurround=\skip108
\backup@length=\skip109
\ar@cellbox=\box114
)
(D:\miktex\tex/latex/booktabs\booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen291
\lightrulewidth=\dimen292
\cmidrulewidth=\dimen293
\belowrulesep=\dimen294
\belowbottomsep=\dimen295
\aboverulesep=\dimen296
\abovetopsep=\dimen297
\cmidrulesep=\dimen298
\cmidrulekern=\dimen299
\defaultaddspace=\dimen300
\@cmidla=\count328
\@cmidlb=\count329
\@aboverulesep=\dimen301
\@belowrulesep=\dimen302
\@thisruleclass=\count330
\@lastruleclass=\count331
\@thisrulewidth=\dimen303
)
(D:\miktex\tex/latex/l3backend\l3backend-pdftex.def
File: l3backend-pdftex.def 2024-01-04 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count332
\l__pdf_internal_box=\box115
)
No file test_compile.aux.
\openout1 = `test_compile.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 35.
LaTeX Font Info:    ... okay on input line 35.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 35.
LaTeX Font Info:    ... okay on input line 35.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 35.
LaTeX Font Info:    ... okay on input line 35.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 35.
LaTeX Font Info:    ... okay on input line 35.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 35.
LaTeX Font Info:    ... okay on input line 35.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 35.
LaTeX Font Info:    ... okay on input line 35.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 35.
LaTeX Font Info:    ... okay on input line 35.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 35.
LaTeX Font Info:    ... okay on input line 35.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 35.
LaTeX Font Info:    ... okay on input line 35.
LaTeX Info: Redefining \degres on input line 35.

Package french.ldf Warning: Please load the "natbib" package
(french.ldf)                BEFORE babel/french; reported on input line 35.

LaTeX Info: Redefining \up on input line 35.
(D:\miktex\tex/context/base/mkii\supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count333
\scratchdimen=\dimen304
\scratchbox=\box116
\nofMPsegments=\count334
\nofMParguments=\count335
\everyMPshowfont=\toks39
\MPscratchCnt=\count336
\MPscratchDim=\dimen305
\MPnumerator=\count337
\makeMPintoPDFobject=\count338
\everyMPtoPDFconversion=\toks40
) (D:\miktex\tex/latex/epstopdf-pkg\epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 4
85.

(D:\miktex\tex/latex/00miktex\epstopdf-sys.cfg
File: epstopdf-sys.cfg 2021/03/18 v2.0 Configuration of epstopdf for MiKTeX
))
Package hyperref Info: Link coloring OFF on input line 35.
\@outlinefile=\write5
\openout5 = `test_compile.out'.


*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: a4paper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(89.62709pt, 418.25368pt, 89.6271pt)
* v-part:(T,H,B)=(101.40665pt, 591.5302pt, 152.11pt)
* \paperwidth=597.50787pt
* \paperheight=845.04684pt
* \textwidth=418.25368pt
* \textheight=591.5302pt
* \oddsidemargin=17.3571pt
* \evensidemargin=17.3571pt
* \topmargin=-7.86334pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=12.0pt
* \footskip=30.0pt
* \marginparwidth=35.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.8pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

\c@minilofdepth=\count339
\c@minilotdepth=\count340
\c@partlofdepth=\count341
\c@partlotdepth=\count342
\c@sectlofdepth=\count343
\c@sectlotdepth=\count344
Package minitoc(hints) Info: I0049
(minitoc(hints))             ==> You requested the hints option. 
(minitoc(hints))             Some hints are eventually given below.
Package minitoc(hints) Info: I0042
(minitoc(hints))             --- The appendix package is loaded. 
(minitoc(hints))             See the minitoc package documentation 
(minitoc(hints))             for specific precautions.
Package minitoc(hints) Info: I0046
(minitoc(hints))             --- The tocbibind package is loaded. 
(minitoc(hints))             See the minitoc package documentation 
(minitoc(hints))             for specific precautions.
Package minitoc(hints) Info: I0047
(minitoc(hints))             --- The tocloft package is loaded. 
(minitoc(hints))             See the minitoc package documentation 
(minitoc(hints))             for specific precautions.

Package minitoc(hints) Warning: W0099
(minitoc(hints))                --- The titlesec package is loaded. 
(minitoc(hints))                It is incompatible 
(minitoc(hints))                with the minitoc package.

No file test_compile.toc.
Package etoc Info: Setting (or re-setting) the options `maintoctotoc' and
(etoc)             `localtoctotoc' to true as tocbibind was detected and
(etoc)             found to be configured for `TOC to toc'.
(etoc)             Reported at begin document on input line 35.
Chapitre 1.
LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <12> on input line 44.
LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <8> on input line 44.
LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <6> on input line 44.
Package minitoc Info: I0009
(minitoc)             Listing minitoc auxiliary files. 
(minitoc)             Creating the test_compile.maf file.
\openout3 = `test_compile.maf'.


Package minitoc(hints) Warning: W0024
(minitoc(hints))                Some hints have been written 
(minitoc(hints))                in the test_compile.log file.

[1


{C:/Users/<USER>/AppData/Local/MiKTeX/fonts/map/pdftex/pdftex.map}]
(test_compile.aux)
 ***********
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-01-04>
 ***********


Package rerunfilecheck Warning: File `test_compile.out' has changed.
(rerunfilecheck)                Rerun to get outlines right
(rerunfilecheck)                or use package `bookmark'.

Package rerunfilecheck Info: Checksums for `test_compile.out':
(rerunfilecheck)             Before: <no file>
(rerunfilecheck)             After:  82EDE3ADCF02944BE6BD8E94B0D6DEC2;106.
 ) 
Here is how much of TeX's memory you used:
 26678 strings out of 474486
 488837 string characters out of 5760170
 1931542 words of memory out of 5000000
 48567 multiletter control sequences out of 15000+600000
 564566 words of font info for 50 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 84i,8n,87p,428b,678s stack positions out of 10000i,1000n,20000p,200000b,200000s
 <C:\Users\<USER>\AppData\Local\MiKTeX\fonts/pk/ljfour/jknappen/ec/dpi600\ecbx
1200.pk> <C:\Users\<USER>\AppData\Local\MiKTeX\fonts/pk/ljfour/jknappen/ec/dpi60
0\eccc1200.pk> <C:\Users\<USER>\AppData\Local\MiKTeX\fonts/pk/ljfour/jknappen/ec
/dpi600\ecrm1200.pk> <C:\Users\<USER>\AppData\Local\MiKTeX\fonts/pk/ljfour/jknap
pen/ec/dpi600\ecbx2488.pk>
Output written on test_compile.pdf (1 page, 26650 bytes).
PDF statistics:
 96 PDF objects out of 1000 (max. 8388607)
 5 named destinations out of 1000 (max. 500000)
 13 words of extra memory for PDF output out of 10000 (max. 10000000)

