# Enhanced Renewable Energy Financial Model - Requirements
# Professional Edition for Consulting Firms

# Core dependencies
flet>=0.21.0
numpy>=1.24.0
pandas>=2.0.0
scipy>=1.10.0
xlsxwriter>=3.1.0
numpy-financial>=1.0.0

# Data visualization
plotly>=5.15.0
matplotlib>=3.7.0
seaborn>=0.12.0

# Data handling and analysis
openpyxl>=3.1.0
pyarrow>=12.0.0
fastparquet>=2023.4.0

# Document export
python-docx>=0.8.11

# Statistical analysis
statsmodels>=0.14.0
scikit-learn>=1.3.0

# Date and time handling
python-dateutil>=2.8.0

# JSON and configuration
pydantic>=2.0.0
pyyaml>=6.0

# Optional: For advanced features
# Monte Carlo and risk analysis
risk-models>=0.1.0  # If available

# Financial calculations
quantlib-python>=1.31  # If needed for advanced financial functions

# Development and testing (optional)
pytest>=7.4.0
pytest-cov>=4.1.0
black>=23.0.0
flake8>=6.0.0
mypy>=1.5.0

# Documentation (optional)
sphinx>=7.0.0
sphinx-rtd-theme>=1.3.0