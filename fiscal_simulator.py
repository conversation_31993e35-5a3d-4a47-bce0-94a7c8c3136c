"""fiscal_simulator.py

Simulation fiscale transfrontalière Italie-Maroc pour un projet solaire PV 10 MW,
analysant les impacts des dispositifs incitatifs dans le cadre du Piano Mattei
et de la Charte d'Investissement marocaine.

Ce script:
1. Simule la fiscalité du projet sur 25 ans
2. Compare trois scénarios: de base, optimisé, non-éligible Piano Mattei
3. Calcule le TRI après impôts, les économies fiscales et le taux d'IS effectif
4. Génère un graphique de comparaison et un tableau récapitulatif

Dépendances: pandas, numpy, matplotlib, openpyxl (pour lecture Excel)
"""

import os
from pathlib import Path

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd

# Pour les calculs financiers (TRI/IRR)
try:
    import numpy_financial as npf
except ImportError:
    print("Le module numpy_financial n'est pas installé. Installation automatique...")
    import pip
    pip.main(['install', 'numpy-financial'])
    import numpy_financial as npf

# Configuration
INPUT_EXCEL = "solar_financial_model.xlsx"
OUTPUT_DIR = Path("figures")
OUTPUT_DIR.mkdir(exist_ok=True)


def load_project_data(excel_path):
    """Charge les données du projet depuis l'Excel (ou utilise des valeurs par défaut)"""
    try:
        # Tente de charger depuis Excel si disponible
        xls = pd.ExcelFile(excel_path)
        inputs = pd.read_excel(xls, sheet_name="Inputs").set_index("Assumption")
        cash = pd.read_excel(xls, sheet_name="Cashflow")
        
        # Extraire les valeurs clés des inputs
        inputs_dict = inputs["Value"].to_dict()
        return {
            "capex": inputs_dict.get("capex_meur", 9.5) * 1_000_000,
            "opex_annual": inputs_dict.get("opex_keuros", 285) * 1_000,
            "revenue_annual": cash.loc[1:, "Revenue"].mean() if "Revenue" in cash.columns else 1_350_000,
            "ebitda_annual": cash.loc[1:, "EBITDA"].mean() if "EBITDA" in cash.columns else 1_065_000,
            "grant_it": inputs_dict.get("grant_meur_italy", 0.95) * 1_000_000,
            "grant_ma": inputs_dict.get("grant_meur_maroc", 1.5) * 1_000_000,
            "equity": (inputs_dict.get("capex_meur", 9.5) - 
                      inputs_dict.get("grant_meur_italy", 0.95) - 
                      inputs_dict.get("grant_meur_maroc", 1.5)) * (1 - inputs_dict.get("debt_ratio", 0.6)) * 1_000_000,
        }
    except Exception as e:
        print(f"Échec de chargement de l'Excel ({e}), utilisation des valeurs par défaut")
        # Valeurs par défaut si Excel indisponible
        return {
            "capex": 9_500_000,
            "opex_annual": 285_000, 
            "revenue_annual": 1_350_000,
            "ebitda_annual": 1_065_000,
            "grant_it": 950_000,
            "grant_ma": 1_500_000, 
            "equity": 2_050_000,
        }


class TaxSimulator:
    """Simulateur de fiscalité transfrontalière Italie-Maroc"""
    
    def __init__(self, project_data):
        self.data = project_data
        self.years = list(range(26))  # 0-25 (année 0 = construction)
        
        # Paramètres fiscaux
        self.it_tax_rate = 0.24  # Taux IS Italie (IRES)
        self.ma_tax_rate_full = 0.25  # Taux IS Maroc normal
        self.ma_tax_rate_reduced = 0.15  # Taux IS Maroc réduit (ZAI)
        self.wht_rate = 0.10  # Taux retenue source dividendes convention Italie-Maroc
        
        # Amortissements comptables vs fiscaux
        self.acc_depr_years = 20  # Amortissement comptable (ans)
        self.acc_depr_rate = 1.0 / self.acc_depr_years
        self.tax_depr_years_italy = 20  # Amortissement fiscal Italie
        self.tax_depr_years_morocco = 15  # Amortissement fiscal accéléré Maroc (énergies vertes)

    def simulate_base_scenario(self):
        """Scénario de base: régime fiscal standard sans optimisations Piano Mattei"""
        df = pd.DataFrame(index=self.years)
        
        # Amortissement standard (pas d'accélération fiscale)
        depr_annual = self.data["capex"] * self.acc_depr_rate
        
        # Données annuelles par pays
        ma_ebitda = [0] + [self.data["ebitda_annual"]] * 25  # année 0 = construction
        ma_depr = [0] + [depr_annual] * 25
        ma_ebit = [a - b for a, b in zip(ma_ebitda, ma_depr)]
        
        # TVA - taux réduit pour électricité renouvelable (10% en 2025-2026)
        # Simulation impact trésorerie (neutralité après remboursement)
        tva_impact = []
        for yr in range(26):
            if yr == 0:  # Phase construction: TVA déductible sur CAPEX
                tva_impact.append(-self.data["capex"] * 0.20 * 0.25)  # 25% non récupéré immédiatement
            elif yr == 1:  # Remboursement crédit TVA
                tva_impact.append(self.data["capex"] * 0.20 * 0.25)
            else:
                tva_impact.append(0)  # Neutralité après exploitation
        
        # IS Maroc - application des taux réels CGI 2024
        ma_tax = []
        for yr, ebit in enumerate(ma_ebit):
            if ebit <= 0:
                ma_tax.append(0)
            elif yr <= 5:  # Exonération 5 premières années (Loi 03-22)
                ma_tax.append(0)
            else:
                # Taux standard 20% jusqu'à 100M MAD (~10M€), 35% au-delà (CGI 2024-2026)
                if ebit < 1_000_000:  # Simplification: seuil 1M€
                    ma_tax.append(ebit * 0.20)
                else:
                    ma_tax.append(1_000_000 * 0.20 + (ebit - 1_000_000) * 0.35)
        
        # Taxe carbone (prévue 2026-2027) - avantage compétitif EnR
        carbon_tax_savings = []
        for yr in range(26):
            if yr <= 6:  # Avant introduction de la taxe
                carbon_tax_savings.append(0)
            else:
                # Simulation économie vs centrale thermique équivalente (~5€/tCO2 au début)
                carbon_tax = 5 * (yr - 6)  # Augmentation progressive 5€/an
                carbon_tax_savings.append(10_800 * min(carbon_tax, 50))  # Plafond 50€/tCO2
        
        # Dividendes rapatriés (EBIT - IS + économies taxe carbone)
        dividends = [max(0, ebit - tax + carbon) 
                    for ebit, tax, carbon in zip(ma_ebit, ma_tax, carbon_tax_savings)]
        
        # Retenue à la source (WHT) sur dividendes (10% - convention Italie-Maroc)
        wht = [div * self.wht_rate for div in dividends]
        
        # IS Italien (IRES 24% avec crédit d'impôt pour taxes payées à l'étranger)
        it_tax = [max(0, div * self.it_tax_rate - w) for div, w in zip(dividends, wht)]
        
        # Charge fiscale totale (IS Maroc + WHT + IS Italien résiduel)
        total_tax = [a + b + c for a, b, c in zip(ma_tax, wht, it_tax)]
        
        # Cash-flow après impôts
        after_tax_cf = [-self.data["equity"]] + [ma_ebitda[i] - total_tax[i] + tva_impact[i] for i in range(1, 26)]
        
        # Stockage des résultats
        df["EBITDA_MA"] = ma_ebitda
        df["IS_MA"] = ma_tax
        df["Économie_Carbone"] = carbon_tax_savings
        df["Dividendes"] = dividends
        df["WHT"] = wht
        df["IS_IT"] = it_tax
        df["Total_Tax"] = total_tax
        df["Impact_TVA"] = tva_impact
        df["CF_après_impôts"] = after_tax_cf
        
        # Calcul KPIs
        calculated_irr = npf.irr(after_tax_cf)
        tax_rate_eff = sum(total_tax[1:]) / sum(ma_ebitda[1:])  # Taux effectif d'imposition
        
        return {
            "name": "Standard",
            "df": df,
            "irr": calculated_irr,
            "tax_rate_eff": tax_rate_eff,
            "total_tax": sum(total_tax),
        }

    def simulate_optimized_scenario(self):
        """Scénario optimisé: utilisation complète des dispositifs Piano Mattei et Charte Invest"""
        df = pd.DataFrame(index=self.years)
        
        # Avantages fiscaux Piano Mattei - SIMEST
        # Source: Mesure Africa (https://www.simest.it/per-le-imprese/finanziamenti-agevolati/potenziamento-mercati-africani)
        # 1. Subvention non-remboursable (10-20% selon la région italienne)
        simest_grant_rate = 0.10  # 10% standard, jusqu'à 20% pour Sud Italie
        simest_grant = min(self.data["capex"] * simest_grant_rate, 100_000)  # Plafond 100k€
        
        # 2. Prêt bonifié (taux préférentiel 2,5% vs marché 5-6%)
        simest_interest_benefit = self.data["capex"] * 0.36 * 0.03 * 5  # 36% du CAPEX, économie 3% sur 5 ans
        
        # 3. Amortissement fiscal accéléré Maroc (15 ans pour EnR vs 20 ans standard)
        ma_depr_rate = 1.0 / self.tax_depr_years_morocco
        ma_depr_annual = self.data["capex"] * ma_depr_rate
        
        # 4. Exonération d'IS ZAI - Zone d'Accélération Industrielle (CGI Art. 6(II-A-1°))
        # 5 ans exonération + taux réduit 15% ensuite (vs 20-35% standard)
        
        # 5. Avantages Piano Mattei spécifiques
        # Crédit d'impôt formation personnel local (SIMEST - 50% des coûts)
        formation_costs = self.data["capex"] * 0.02  # 2% du CAPEX = formation
        it_training_credit = formation_costs * 0.50  # 50% des coûts éligibles
        
        # Crédit d'impôt internationalisation (15% des coûts de développement) 
        dev_costs = self.data["capex"] * 0.04  # 4% du CAPEX = développement
        it_dev_credit = dev_costs * 0.15  # 15% déductible
        
        # Avantage garantie SACE (couverture risque 80% - économie prime de risque 2%)
        sace_benefit = self.data["capex"] * 0.50 * 0.02 * 3  # 50% dette, prime 2%, 3 ans
        
        # TVA - identique au scénario de base (taux réduit 10% électricité EnR 2025-2026)
        tva_impact = []
        for yr in range(26):
            if yr == 0:  # Construction
                tva_impact.append(-self.data["capex"] * 0.20 * 0.15)  # Récupération plus rapide avec statut exportateur
            elif yr == 1:  # Remboursement
                tva_impact.append(self.data["capex"] * 0.20 * 0.15)
            else:
                tva_impact.append(0)
        
        # Données annuelles EBITDA et amortissements
        ma_ebitda = [0] + [self.data["ebitda_annual"]] * 25
        ma_depr = [0] + [ma_depr_annual] * self.tax_depr_years_morocco + [0] * (25 - self.tax_depr_years_morocco)
        ma_ebit = [a - b for a, b in zip(ma_ebitda, ma_depr)]
        
        # Taxe carbone (comme scénario de base)
        carbon_tax_savings = []
        for yr in range(26):
            if yr <= 6:
                carbon_tax_savings.append(0)
            else:
                carbon_tax = 5 * (yr - 6)
                carbon_tax_savings.append(10_800 * min(carbon_tax, 50))
        
        # IS Maroc - régime ZAI (exonération 5 ans + taux réduit 15%)
        ma_tax = []
        for yr, ebit in enumerate(ma_ebit):
            if ebit <= 0:
                ma_tax.append(0)
            elif yr <= 5:  # Exonération 5 ans ZAI
                ma_tax.append(0)
            else:
                # Taux ZAI réduit (15%) quel que soit le montant
                ma_tax.append(ebit * self.ma_tax_rate_reduced)
        
        # Dividendes rapatriés (EBIT - IS + économies carbone + avantages SIMEST/SACE)
        # Ajout des avantages financiers aux années appropriées
        dividend_boost = [0] * 26
        dividend_boost[1] += simest_grant + it_training_credit + it_dev_credit  # Année 1
        
        # Répartition des autres avantages sur 5 ans
        for yr in range(1, 6):
            dividend_boost[yr] += (simest_interest_benefit + sace_benefit) / 5
        
        dividends = [max(0, ebit - tax + carbon + boost) 
                    for ebit, tax, carbon, boost in zip(ma_ebit, ma_tax, carbon_tax_savings, dividend_boost)]
        
        # WHT optimisée (convention fiscale Italie-Maroc = 10%)
        wht = [div * self.wht_rate for div in dividends]
        
        # IS Italien (taux réduit 21,5% via patent box pour technologies vertes, Loi 160/2019)
        it_tax_rate_reduced = 0.215  # 21,5% vs 24% standard
        it_tax = [max(0, div * it_tax_rate_reduced - w) for div, w in zip(dividends, wht)]
        
        # Charge fiscale totale
        total_tax = [a + b + c for a, b, c in zip(ma_tax, wht, it_tax)]
        
        # Cash-flow après impôts (equity réduit par la subvention)
        equity_reduced = self.data["equity"] - simest_grant
        after_tax_cf = [-equity_reduced] + [ma_ebitda[i] - total_tax[i] + tva_impact[i] for i in range(1, 26)]
        
        # Stockage des résultats
        df["EBITDA_MA"] = ma_ebitda
        df["IS_MA"] = ma_tax
        df["Économie_Carbone"] = carbon_tax_savings
        df["Avantages_Piano_Mattei"] = dividend_boost
        df["Dividendes"] = dividends
        df["WHT"] = wht
        df["IS_IT"] = it_tax
        df["Total_Tax"] = total_tax
        df["Impact_TVA"] = tva_impact
        df["CF_après_impôts"] = after_tax_cf
        
        # Calcul KPIs
        calculated_irr = npf.irr(after_tax_cf)
        tax_rate_eff = sum(total_tax[1:]) / sum(ma_ebitda[1:])  # Taux effectif d'imposition
        
        return {
            "name": "Piano Mattei + ZAI",
            "df": df,
            "irr": calculated_irr,
            "tax_rate_eff": tax_rate_eff,
            "total_tax": sum(total_tax),
        }

    def simulate_no_piano_mattei(self):
        """Scénario sans Piano Mattei: uniquement dispositifs marocains"""
        df = pd.DataFrame(index=self.years)
        
        # Amortissement standard (pas d'accélération fiscale)
        depr_annual = self.data["capex"] * self.acc_depr_rate
        
        # Absence d'avantages italiens
        # - Pas de subvention SIMEST (fonds perdus)
        # - Pas de prêt bonifié (taux marché normal 5-6%)
        # - Pas de garantie SACE (couverture risque pays réduite)
        # - Pas de crédits d'impôt pour internationalisation
        # Impact financier direct: financement plus cher, prime de risque plus élevée
        interest_penalty = self.data["capex"] * 0.60 * 0.03 * 5  # 60% dette, pénalité 3%/an, 5 ans
        risk_premium_penalty = self.data["capex"] * 0.60 * 0.02 * 5  # Prime risque 2%/an supplémentaire
        
        # TVA - comme scénario de base mais sans avantages exportateur
        tva_impact = []
        for yr in range(26):
            if yr == 0:  # Phase construction: TVA déductible sur CAPEX
                tva_impact.append(-self.data["capex"] * 0.20 * 0.35)  # Récupération plus lente (35% non récupéré immédiatement)
            elif yr == 1:  # Remboursement 1ère tranche
                tva_impact.append(self.data["capex"] * 0.20 * 0.15)
            elif yr == 2:  # Remboursement 2e tranche
                tva_impact.append(self.data["capex"] * 0.20 * 0.20)  # Délai plus long
            else:
                tva_impact.append(0)
        
        # Données annuelles EBITDA et amortissements
        ma_ebitda = [0] + [self.data["ebitda_annual"]] * 25
        ma_depr = [0] + [depr_annual] * 25
        ma_ebit = [a - b for a, b in zip(ma_ebitda, ma_depr)]
        
        # Taxe carbone (comme scénario de base)
        carbon_tax_savings = []
        for yr in range(26):
            if yr <= 6:
                carbon_tax_savings.append(0)
            else:
                carbon_tax = 5 * (yr - 6)
                carbon_tax_savings.append(10_800 * min(carbon_tax, 50))
        
        # IS Maroc - bénéfice de l'exonération 5 ans mais taux normal ensuite
        ma_tax = []
        for yr, ebit in enumerate(ma_ebit):
            if ebit <= 0:
                ma_tax.append(0)
            elif yr <= 5:  # Exonération 5 ans standard
                ma_tax.append(0)
            else:
                # Taux normal 20%/35% (sans ZAI)
                if ebit < 1_000_000:  # Seuil 1M€
                    ma_tax.append(ebit * 0.20)
                else:
                    ma_tax.append(1_000_000 * 0.20 + (ebit - 1_000_000) * 0.35)
        
        # Pénalités financières réparties sur 5 premières années
        financial_penalties = [0] * 26
        for yr in range(1, 6):
            financial_penalties[yr] = -(interest_penalty + risk_premium_penalty) / 5
        
        # Dividendes rapatriés (EBIT - IS + économies carbone - pénalités financières)
        dividends = [max(0, ebit - tax + carbon + penalty) 
                    for ebit, tax, carbon, penalty in zip(ma_ebit, ma_tax, carbon_tax_savings, financial_penalties)]
        
        # WHT standard plus élevée (15% au lieu de 10% - sans convention fiscale avantageuse)
        wht_rate_high = 0.15  # 15% au lieu de 10%
        wht = [div * wht_rate_high for div in dividends]
        
        # IS Italien standard (24%) sans réductions ni patent box
        it_tax_rate_std = 0.24
        it_tax = [max(0, div * it_tax_rate_std - w) for div, w in zip(dividends, wht)]
        
        # Charge fiscale totale
        total_tax = [a + b + c for a, b, c in zip(ma_tax, wht, it_tax)]
        
        # Cash-flow après impôts (equity plus élevé sans subventions)
        # La subvention italienne doit être compensée par plus de fonds propres
        equity_no_pm = self.data["equity"] + self.data["grant_it"]  # Besoin de compenser la subvention manquante
        after_tax_cf = [-equity_no_pm] + [ma_ebitda[i] - total_tax[i] + tva_impact[i] + financial_penalties[i] for i in range(1, 26)]
        
        # Stockage des résultats
        df["EBITDA_MA"] = ma_ebitda
        df["IS_MA"] = ma_tax
        df["Économie_Carbone"] = carbon_tax_savings
        df["Pénalités_Financières"] = financial_penalties
        df["Dividendes"] = dividends
        df["WHT"] = wht
        df["IS_IT"] = it_tax
        df["Total_Tax"] = total_tax
        df["Impact_TVA"] = tva_impact
        df["CF_après_impôts"] = after_tax_cf
        
        # Calcul KPIs
        calculated_irr = npf.irr(after_tax_cf)
        tax_rate_eff = sum(total_tax[1:]) / sum(ma_ebitda[1:])  # Taux effectif d'imposition
        
        return {
            "name": "Sans Piano Mattei",
            "df": df,
            "irr": calculated_irr,
            "tax_rate_eff": tax_rate_eff,
            "total_tax": sum(total_tax),
        }

    def run_all_scenarios(self):
        """Exécute tous les scénarios et compare les résultats"""
        base = self.simulate_base_scenario()
        optimized = self.simulate_optimized_scenario()
        no_pm = self.simulate_no_piano_mattei()
        
        return [base, optimized, no_pm]

    def plot_comparison(self, scenarios):
        """Génère un graphique comparatif des scénarios"""
        plt.figure(figsize=(10, 6))
        
        # Barres TRI
        names = [s["name"] for s in scenarios]
        irrs = [s["irr"] * 100 for s in scenarios]
        tax_rates = [s["tax_rate_eff"] * 100 for s in scenarios]
        tax_savings = [scenarios[0]["total_tax"] - s["total_tax"] for s in scenarios]
        
        # Graphique principal: TRI
        bars = plt.bar(names, irrs, color=['#3498db', '#2ecc71', '#e74c3c'])
        plt.ylabel('TRI après impôts (%)')
        plt.title('Impact des dispositifs fiscaux sur le TRI')
        
        # Ajouter labels
        for bar, irr, tax in zip(bars, irrs, tax_rates):
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height + 0.3,
                    f'{irr:.1f}%\n(IS eff: {tax:.1f}%)',
                    ha='center', va='bottom', fontsize=10)
        
        plt.ylim(0, max(irrs) * 1.2)
        plt.grid(axis='y', linestyle='--', alpha=0.7)
        plt.tight_layout()
        
        # Enregistrer
        output_path = OUTPUT_DIR / "fiscal_impact.png"
        plt.savefig(output_path, dpi=300)
        plt.close()
        
        return output_path

    def generate_summary_table(self, scenarios):
        """Génère un tableau récapitulatif des résultats fiscaux"""
        data = {
            "Scénario": [s["name"] for s in scenarios],
            "TRI après impôts": [f"{s['irr']*100:.2f}%" for s in scenarios],
            "Taux d'IS effectif": [f"{s['tax_rate_eff']*100:.2f}%" for s in scenarios],
            "Charge fiscale totale": [f"{s['total_tax']/1000:.0f} k€" for s in scenarios],
            "Économie fiscale vs Base": ["Référence"] + [f"{(scenarios[0]['total_tax']-s['total_tax'])/1000:.0f} k€" for s in scenarios[1:]]
        }
        
        return pd.DataFrame(data)


if __name__ == "__main__":
    # Charger les données du projet
    project_data = load_project_data(INPUT_EXCEL)
    
    # Initialiser et exécuter la simulation
    simulator = TaxSimulator(project_data)
    scenarios = simulator.run_all_scenarios()
    
    # Générer graphique et tableau
    chart_path = simulator.plot_comparison(scenarios)
    summary_table = simulator.generate_summary_table(scenarios)
    
    # Afficher les résultats
    print("\n=== RÉSULTATS DE LA SIMULATION FISCALE TRANSFRONTALIÈRE ===\n")
    print(summary_table.to_string(index=False))
    print(f"\nGraphique enregistré: {chart_path}")
    
    # Export CSV
    csv_path = OUTPUT_DIR / "fiscal_summary.csv"
    summary_table.to_csv(csv_path, index=False)
    print(f"Tableau exporté: {csv_path}")
