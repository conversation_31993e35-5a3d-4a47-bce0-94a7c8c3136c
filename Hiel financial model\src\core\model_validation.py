from __future__ import annotations

import warnings
from dataclasses import dataclass
from typing import Dict, <PERSON>, Tuple, Optional, Any
import numpy as np
import pandas as pd
from datetime import datetime

from .enhanced_data_models import EnhancedProjectAssumptions, ModelValidation

@dataclass
class ValidationResult:
    """Result of model validation"""
    is_valid: bool
    warnings: List[str]
    errors: List[str]
    benchmarks: Dict[str, Any]
    recommendations: List[str]
    validation_date: datetime
    
    def __post_init__(self):
        if self.validation_date is None:
            self.validation_date = datetime.now()

class IndustryBenchmarks:
    """Industry benchmarks for renewable energy projects"""
    
    # LCOE benchmarks (EUR/kWh) - 2024 data
    LCOE_BENCHMARKS = {
        "solar_pv_utility": {
            "global_average": 0.044,
            "mena_region": 0.038,
            "morocco_range": (0.035, 0.050),
            "competitive_threshold": 0.045
        },
        "wind_onshore": {
            "global_average": 0.033,
            "mena_region": 0.030,
            "morocco_range": (0.028, 0.040)
        }
    }
    
    # WACC benchmarks by region and risk profile
    WACC_BENCHMARKS = {
        "developed_markets": {
            "low_risk": (0.06, 0.08),
            "medium_risk": (0.08, 0.10),
            "high_risk": (0.10, 0.12)
        },
        "emerging_markets": {
            "low_risk": (0.08, 0.10),
            "medium_risk": (0.10, 0.12),
            "high_risk": (0.12, 0.16)
        },
        "africa": {
            "average": 0.156,
            "range": (0.12, 0.20),
            "morocco_typical": (0.09, 0.12)
        }
    }
    
    # IRR benchmarks for equity investors
    IRR_BENCHMARKS = {
        "utility_scale_solar": {
            "sponsor_target": (0.12, 0.18),
            "financial_investor": (0.10, 0.15),
            "minimum_acceptable": 0.08
        },
        "by_region": {
            "developed": (0.08, 0.12),
            "emerging": (0.12, 0.18),
            "africa": (0.15, 0.25)
        }
    }
    
    # CAPEX benchmarks (EUR/MW)
    CAPEX_BENCHMARKS = {
        "solar_pv_utility": {
            "global_2024": 800_000,
            "range_2024": (600_000, 1_200_000),
            "morocco_typical": (700_000, 900_000),
            "target_2030": 600_000
        }
    }
    
    # Operational benchmarks
    OPERATIONAL_BENCHMARKS = {
        "capacity_factor": {
            "solar_pv_morocco": (0.25, 0.35),
            "solar_pv_ouarzazate": (0.28, 0.32),
            "solar_pv_dakhla": (0.30, 0.35)
        },
        "degradation": {
            "annual_typical": 0.005,
            "annual_range": (0.003, 0.008),
            "first_year": (0.02, 0.03)
        },
        "availability": {
            "target": 0.98,
            "minimum": 0.95
        }
    }
    
    # Financial structure benchmarks
    FINANCIAL_BENCHMARKS = {
        "debt_ratio": {
            "typical": (0.65, 0.75),
            "maximum_recommended": 0.80
        },
        "dscr": {
            "minimum_covenant": 1.20,
            "target": 1.35,
            "comfortable": 1.50
        },
        "debt_tenor": {
            "typical_years": (15, 20),
            "maximum_years": 25
        }
    }

class ModelValidator:
    """Comprehensive model validation against industry benchmarks"""
    
    def __init__(self):
        self.benchmarks = IndustryBenchmarks()
    
    def validate_comprehensive(self, 
                             assumptions: EnhancedProjectAssumptions,
                             kpis: Dict[str, float],
                             cashflow: pd.DataFrame) -> ValidationResult:
        """Perform comprehensive model validation"""
        
        warnings_list = []
        errors_list = []
        recommendations = []
        benchmark_results = {}
        
        # 1. Validate assumptions
        assumption_warnings = self._validate_assumptions(assumptions)
        warnings_list.extend(assumption_warnings)
        
        # 2. Validate KPIs against benchmarks
        kpi_validation = self._validate_kpis(kpis, assumptions)
        warnings_list.extend(kpi_validation['warnings'])
        errors_list.extend(kpi_validation['errors'])
        benchmark_results.update(kpi_validation['benchmarks'])
        
        # 3. Validate financial structure
        financial_validation = self._validate_financial_structure(assumptions, cashflow)
        warnings_list.extend(financial_validation['warnings'])
        recommendations.extend(financial_validation['recommendations'])
        
        # 4. Validate operational parameters
        operational_validation = self._validate_operational_parameters(assumptions)
        warnings_list.extend(operational_validation['warnings'])
        
        # 5. Cross-validation checks
        cross_validation = self._cross_validate(assumptions, kpis)
        warnings_list.extend(cross_validation['warnings'])
        recommendations.extend(cross_validation['recommendations'])
        
        # Determine overall validity
        is_valid = len(errors_list) == 0
        
        return ValidationResult(
            is_valid=is_valid,
            warnings=warnings_list,
            errors=errors_list,
            benchmarks=benchmark_results,
            recommendations=recommendations,
            validation_date=datetime.now()
        )
    
    def _validate_assumptions(self, assumptions: EnhancedProjectAssumptions) -> List[str]:
        """Validate basic assumptions"""
        warnings = []
        
        # CAPEX validation
        capex_per_mw = assumptions.capex_meur * 1_000_000 / assumptions.capacity_mw
        capex_range = self.benchmarks.CAPEX_BENCHMARKS['solar_pv_utility']['range_2024']
        
        if capex_per_mw < capex_range[0]:
            warnings.append(f"CAPEX per MW ({capex_per_mw:,.0f} EUR/MW) is below typical range ({capex_range[0]:,.0f}-{capex_range[1]:,.0f} EUR/MW)")
        elif capex_per_mw > capex_range[1]:
            warnings.append(f"CAPEX per MW ({capex_per_mw:,.0f} EUR/MW) exceeds typical range ({capex_range[0]:,.0f}-{capex_range[1]:,.0f} EUR/MW)")
        
        # Capacity factor validation
        cf = assumptions.production_mwh_year1 / (assumptions.capacity_mw * 8760)
        cf_range = self.benchmarks.OPERATIONAL_BENCHMARKS['capacity_factor']['solar_pv_morocco']
        
        if cf < cf_range[0] or cf > cf_range[1]:
            warnings.append(f"Capacity factor ({cf:.1%}) outside typical Morocco range ({cf_range[0]:.1%}-{cf_range[1]:.1%})")
        
        # WACC validation
        wacc_range = self.benchmarks.WACC_BENCHMARKS['africa']['morocco_typical']
        if assumptions.discount_rate < wacc_range[0] or assumptions.discount_rate > wacc_range[1]:
            warnings.append(f"WACC ({assumptions.discount_rate:.1%}) outside typical Morocco range ({wacc_range[0]:.1%}-{wacc_range[1]:.1%})")
        
        # Debt ratio validation
        debt_range = self.benchmarks.FINANCIAL_BENCHMARKS['debt_ratio']['typical']
        if assumptions.debt_ratio > self.benchmarks.FINANCIAL_BENCHMARKS['debt_ratio']['maximum_recommended']:
            warnings.append(f"Debt ratio ({assumptions.debt_ratio:.1%}) exceeds recommended maximum (80%)")
        elif assumptions.debt_ratio < debt_range[0] or assumptions.debt_ratio > debt_range[1]:
            warnings.append(f"Debt ratio ({assumptions.debt_ratio:.1%}) outside typical range ({debt_range[0]:.1%}-{debt_range[1]:.1%})")
        
        return warnings
    
    def _validate_kpis(self, kpis: Dict[str, float], assumptions: EnhancedProjectAssumptions) -> Dict[str, List]:
        """Validate KPIs against industry benchmarks"""
        warnings = []
        errors = []
        benchmarks = {}
        
        # LCOE validation
        if 'LCOE_eur_kwh' in kpis:
            lcoe = kpis['LCOE_eur_kwh']
            lcoe_benchmark = self.benchmarks.LCOE_BENCHMARKS['solar_pv_utility']
            
            benchmarks['lcoe'] = {
                'value': lcoe,
                'benchmark_range': lcoe_benchmark['morocco_range'],
                'global_average': lcoe_benchmark['global_average'],
                'competitive_threshold': lcoe_benchmark['competitive_threshold']
            }
            
            if lcoe > lcoe_benchmark['competitive_threshold']:
                warnings.append(f"LCOE ({lcoe:.3f} EUR/kWh) exceeds competitive threshold ({lcoe_benchmark['competitive_threshold']:.3f} EUR/kWh)")
            
            if lcoe < lcoe_benchmark['morocco_range'][0] or lcoe > lcoe_benchmark['morocco_range'][1]:
                warnings.append(f"LCOE ({lcoe:.3f} EUR/kWh) outside Morocco typical range ({lcoe_benchmark['morocco_range'][0]:.3f}-{lcoe_benchmark['morocco_range'][1]:.3f} EUR/kWh)")
        
        # IRR validation
        if 'IRR_equity' in kpis:
            irr = kpis['IRR_equity']
            irr_benchmark = self.benchmarks.IRR_BENCHMARKS['utility_scale_solar']
            
            benchmarks['irr_equity'] = {
                'value': irr,
                'sponsor_target_range': irr_benchmark['sponsor_target'],
                'minimum_acceptable': irr_benchmark['minimum_acceptable']
            }
            
            if irr < irr_benchmark['minimum_acceptable']:
                errors.append(f"Equity IRR ({irr:.1%}) below minimum acceptable threshold ({irr_benchmark['minimum_acceptable']:.1%})")
            elif irr < irr_benchmark['sponsor_target'][0]:
                warnings.append(f"Equity IRR ({irr:.1%}) below typical sponsor target range ({irr_benchmark['sponsor_target'][0]:.1%}-{irr_benchmark['sponsor_target'][1]:.1%})")
        
        # DSCR validation
        if 'Min_DSCR' in kpis:
            min_dscr = kpis['Min_DSCR']
            dscr_benchmark = self.benchmarks.FINANCIAL_BENCHMARKS['dscr']
            
            benchmarks['dscr'] = {
                'min_value': min_dscr,
                'minimum_covenant': dscr_benchmark['minimum_covenant'],
                'target': dscr_benchmark['target']
            }
            
            if min_dscr < dscr_benchmark['minimum_covenant']:
                errors.append(f"Minimum DSCR ({min_dscr:.2f}) below typical covenant requirement ({dscr_benchmark['minimum_covenant']:.2f})")
            elif min_dscr < dscr_benchmark['target']:
                warnings.append(f"Minimum DSCR ({min_dscr:.2f}) below target level ({dscr_benchmark['target']:.2f})")
        
        # NPV validation
        if 'NPV_equity' in kpis:
            npv = kpis['NPV_equity']
            if npv < 0:
                errors.append(f"Negative NPV ({npv:,.0f} EUR) indicates project is not viable at current discount rate")
        
        return {
            'warnings': warnings,
            'errors': errors,
            'benchmarks': benchmarks
        }
    
    def _validate_financial_structure(self, assumptions: EnhancedProjectAssumptions, 
                                    cashflow: pd.DataFrame) -> Dict[str, List]:
        """Validate financial structure"""
        warnings = []
        recommendations = []
        
        # Debt service coverage throughout project life
        if 'DSCR' in cashflow.columns:
            dscr_series = cashflow['DSCR'].replace([np.inf, -np.inf], np.nan).dropna()
            if len(dscr_series) > 0:
                avg_dscr = dscr_series.mean()
                if avg_dscr < 1.35:
                    recommendations.append("Consider reducing debt ratio or improving cash flows to achieve higher average DSCR")
        
        # Grant dependency analysis
        grant_percentage = (assumptions.total_grants_meur / assumptions.capex_meur) * 100
        if grant_percentage > 30:
            warnings.append(f"High grant dependency ({grant_percentage:.1f}% of CAPEX) increases project risk")
            recommendations.append("Validate grant assumptions with official documentation and consider scenarios without grants")
        
        # Interest rate vs market conditions
        if assumptions.interest_rate > 0.08:
            recommendations.append("Interest rate appears high - consider shopping for better financing terms or improving project risk profile")
        
        return {
            'warnings': warnings,
            'recommendations': recommendations
        }
    
    def _validate_operational_parameters(self, assumptions: EnhancedProjectAssumptions) -> Dict[str, List]:
        """Validate operational parameters"""
        warnings = []
        
        # Degradation rates
        if assumptions.degradation_annual > 0.008:
            warnings.append(f"Annual degradation rate ({assumptions.degradation_annual:.1%}) is higher than typical (0.5%)")
        
        # O&M costs
        opex_per_mw = assumptions.opex_keuros_year1 * 1000 / assumptions.capacity_mw
        if opex_per_mw > 25000:  # EUR 25k per MW per year
            warnings.append(f"O&M costs ({opex_per_mw:,.0f} EUR/MW/year) appear high for solar PV")
        
        return {'warnings': warnings}
    
    def _cross_validate(self, assumptions: EnhancedProjectAssumptions, 
                       kpis: Dict[str, float]) -> Dict[str, List]:
        """Perform cross-validation checks"""
        warnings = []
        recommendations = []
        
        # LCOE vs PPA price comparison
        if 'LCOE_eur_kwh' in kpis:
            lcoe = kpis['LCOE_eur_kwh']
            ppa_price = assumptions.ppa_price_eur_kwh
            
            margin = (ppa_price - lcoe) / lcoe * 100
            if margin < 10:
                warnings.append(f"Low margin between PPA price and LCOE ({margin:.1f}%) increases project risk")
                recommendations.append("Consider negotiating higher PPA price or reducing costs to improve project margins")
        
        # Debt sizing validation
        debt_amount = assumptions.investment_for_debt_sizing_meur * assumptions.debt_ratio
        if debt_amount <= 0:
            warnings.append("No debt in the structure - consider optimal capital structure for tax benefits")
        
        return {
            'warnings': warnings,
            'recommendations': recommendations
        }

class BenchmarkComparison:
    """Compare project metrics against industry benchmarks"""
    
    def __init__(self):
        self.benchmarks = IndustryBenchmarks()
    
    def generate_benchmark_report(self, kpis: Dict[str, float], 
                                assumptions: EnhancedProjectAssumptions) -> Dict[str, Any]:
        """Generate comprehensive benchmark comparison report"""
        
        report = {
            'project_metrics': kpis,
            'benchmarks': {},
            'rankings': {},
            'recommendations': []
        }
        
        # LCOE benchmarking
        if 'LCOE_eur_kwh' in kpis:
            lcoe = kpis['LCOE_eur_kwh']
            lcoe_benchmarks = self.benchmarks.LCOE_BENCHMARKS['solar_pv_utility']
            
            report['benchmarks']['lcoe'] = {
                'project_value': lcoe,
                'global_average': lcoe_benchmarks['global_average'],
                'mena_average': lcoe_benchmarks['mena_region'],
                'morocco_range': lcoe_benchmarks['morocco_range'],
                'competitive_threshold': lcoe_benchmarks['competitive_threshold']
            }
            
            # Ranking
            if lcoe <= lcoe_benchmarks['mena_region']:
                report['rankings']['lcoe'] = 'Excellent'
            elif lcoe <= lcoe_benchmarks['competitive_threshold']:
                report['rankings']['lcoe'] = 'Competitive'
            elif lcoe <= lcoe_benchmarks['morocco_range'][1]:
                report['rankings']['lcoe'] = 'Acceptable'
            else:
                report['rankings']['lcoe'] = 'Poor'
        
        # IRR benchmarking
        if 'IRR_equity' in kpis:
            irr = kpis['IRR_equity']
            irr_benchmarks = self.benchmarks.IRR_BENCHMARKS['utility_scale_solar']
            
            report['benchmarks']['irr'] = {
                'project_value': irr,
                'sponsor_target_range': irr_benchmarks['sponsor_target'],
                'minimum_acceptable': irr_benchmarks['minimum_acceptable']
            }
            
            # Ranking
            if irr >= irr_benchmarks['sponsor_target'][1]:
                report['rankings']['irr'] = 'Excellent'
            elif irr >= irr_benchmarks['sponsor_target'][0]:
                report['rankings']['irr'] = 'Good'
            elif irr >= irr_benchmarks['minimum_acceptable']:
                report['rankings']['irr'] = 'Acceptable'
            else:
                report['rankings']['irr'] = 'Poor'
        
        # CAPEX benchmarking
        capex_per_mw = assumptions.capex_meur * 1_000_000 / assumptions.capacity_mw
        capex_benchmarks = self.benchmarks.CAPEX_BENCHMARKS['solar_pv_utility']
        
        report['benchmarks']['capex'] = {
            'project_value': capex_per_mw,
            'global_2024': capex_benchmarks['global_2024'],
            'morocco_range': capex_benchmarks['morocco_typical'],
            'target_2030': capex_benchmarks['target_2030']
        }
        
        # Generate recommendations
        report['recommendations'] = self._generate_recommendations(report)
        
        return report
    
    def _generate_recommendations(self, report: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on benchmark comparison"""
        recommendations = []
        
        # LCOE recommendations
        if 'lcoe' in report['rankings']:
            if report['rankings']['lcoe'] == 'Poor':
                recommendations.append("LCOE is above competitive levels - focus on cost reduction strategies")
            elif report['rankings']['lcoe'] == 'Acceptable':
                recommendations.append("LCOE is within acceptable range but could be improved for better competitiveness")
        
        # IRR recommendations
        if 'irr' in report['rankings']:
            if report['rankings']['irr'] == 'Poor':
                recommendations.append("IRR is below acceptable levels - consider improving project economics or reducing risk")
            elif report['rankings']['irr'] == 'Acceptable':
                recommendations.append("IRR meets minimum requirements but may not attract premium investors")
        
        # CAPEX recommendations
        if 'capex' in report['benchmarks']:
            capex = report['benchmarks']['capex']['project_value']
            morocco_range = report['benchmarks']['capex']['morocco_range']
            
            if capex > morocco_range[1]:
                recommendations.append("CAPEX is above Morocco typical range - investigate cost reduction opportunities")
            elif capex < morocco_range[0]:
                recommendations.append("CAPEX is below typical range - verify cost estimates and quality assumptions")
        
        return recommendations

def validate_model_comprehensive(assumptions: EnhancedProjectAssumptions,
                               kpis: Dict[str, float],
                               cashflow: pd.DataFrame) -> ValidationResult:
    """Main function for comprehensive model validation"""
    validator = ModelValidator()
    return validator.validate_comprehensive(assumptions, kpis, cashflow)

def generate_benchmark_report(kpis: Dict[str, float],
                            assumptions: EnhancedProjectAssumptions) -> Dict[str, Any]:
    """Main function for benchmark comparison"""
    comparator = BenchmarkComparison()
    return comparator.generate_benchmark_report(kpis, assumptions)