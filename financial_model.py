"""financial_model.py

Generate a 25-year solar PV project finance model and export it to an Excel
workbook (`solar_financial_model.xlsx`).  Sheets generated:
  • Inputs – key editable assumptions
  • Cashflow – yearly project cash‐flows and debt schedule
  • KPIs – IRR, NPV, LCOE, min DSCR
  • Sensitivity – IRR impact of ±5 / 10 % on PPA price (modifiable)

Run:
    python financial_model.py

Dependencies (add to `requirements.txt`):
    pandas>=2  numpy>=1.24  numpy-financial>=1.0  xlsxwriter
"""

from __future__ import annotations

import math
from dataclasses import asdict, dataclass
from pathlib import Path
from typing import Dict, List

import numpy as np
import pandas as pd

try:
    import numpy_financial as npf
except ImportError:  # minimalist fallback if package missing

    def _npv(rate, values):
        return sum(v / (1 + rate) ** i for i, v in enumerate(values))

    def irr(values, tol=1e-6, maxiter=100):
        rate = 0.1
        for _ in range(maxiter):
            f = _npv(rate, values)
            df = sum(-i * v / (1 + rate) ** (i + 1) for i, v in enumerate(values))
            rate_new = rate - f / df if df else rate
            if abs(rate_new - rate) < tol:
                return rate_new
            rate = rate_new
        return rate

    class npf:  # type: ignore
        irr = staticmethod(irr)
        npv = staticmethod(_npv)


@dataclass
class Assumptions:
    """Key inputs – adjust to test scenarios."""

    years: int = 25
    location: str = "Ouarzazate"  # or "Dakhla"

    # Generation - location specific
    production_mwh_ouarzazate: float = 18_000  # P50 first-year production (MWh)
    production_mwh_dakhla: float = 19_200  # Higher GHI in Dakhla
    degradation: float = 0.004  # 0.4 %/year

    # Prices & costs
    ppa_price_eur_kwh: float = 0.075
    price_escalation: float = 0.02  # EU inflation
    opex_keuros_ouarzazate: float = 285  # year-1 OPEX (€k)
    opex_keuros_dakhla: float = 310  # Higher due to logistics
    opex_escalation: float = 0.02

    # Investment
    capex_meur_ouarzazate: float = 9.5
    capex_meur_dakhla: float = 9.8  # Higher due to logistics
    
    # Incentives breakdown
    grant_meur_italy: float = 0.95  # Piano Mattei subsidy
    grant_meur_masen: float = 0.95  # ~10% of CAPEX for priority zones
    grant_meur_iresen: float = 0.475  # ~5% for innovation components
    grant_meur_connection: float = 0.285  # 30% of connection costs

    # Debt
    debt_ratio: float = 0.60
    interest_rate: float = 0.025
    debt_years: int = 15
    grace_years: int = 2

    # Taxes
    tax_holiday: int = 5
    tax_rate: float = 0.15

    # Discount
    discount_rate: float = 0.08
    
    @property
    def production_mwh(self) -> float:
        return self.production_mwh_ouarzazate if self.location == "Ouarzazate" else self.production_mwh_dakhla
    
    @property
    def opex_keuros(self) -> float:
        return self.opex_keuros_ouarzazate if self.location == "Ouarzazate" else self.opex_keuros_dakhla
    
    @property
    def capex_meur(self) -> float:
        return self.capex_meur_ouarzazate if self.location == "Ouarzazate" else self.capex_meur_dakhla
        
    @property
    def grant_meur_maroc(self) -> float:
        return self.grant_meur_masen + self.grant_meur_iresen + self.grant_meur_connection


def build_cashflow(a: Assumptions) -> pd.DataFrame:
    yrs = np.arange(0, a.years + 1)
    df = pd.DataFrame(index=yrs)

    # CAPEX & grants (year 0 only)
    df["Capex"] = 0.0
    df.loc[0, "Capex"] = -a.capex_meur * 1e6
    df["Grants"] = 0.0
    df.loc[0, "Grants"] = (a.grant_meur_italy + a.grant_meur_maroc) * 1e6

    # Production & price series
    prod = [0] + [a.production_mwh * (1 - a.degradation) ** (y - 1) for y in yrs if y]
    price = [0] + [a.ppa_price_eur_kwh * (1 + a.price_escalation) ** (y - 1) for y in yrs if y]
    df["Prod_MWh"] = prod
    df["Price_EUR_kWh"] = price
    df["Revenue"] = df["Prod_MWh"] * 1_000 * df["Price_EUR_kWh"]

    # OPEX
    opex = [0] + [a.opex_keuros * 1e3 * (1 + a.opex_escalation) ** (y - 1) for y in yrs if y]
    df["OPEX"] = [-v for v in opex]

    # EBITDA
    df["EBITDA"] = df["Revenue"] + df["OPEX"]

    # Debt schedule
    debt_nom = (a.capex_meur - a.grant_meur_italy - a.grant_meur_maroc) * 1e6 * a.debt_ratio
    yearly_principal = debt_nom / (a.debt_years - a.grace_years)
    outstanding = debt_nom
    int_col, prin_col = [0.0], [0.0]
    for y in yrs[1:]:
        interest = -outstanding * a.interest_rate if outstanding else 0.0
        if y <= a.grace_years:
            principal = 0.0
        else:
            principal = -min(yearly_principal, outstanding)
        outstanding += principal  # principal is negative
        int_col.append(interest)
        prin_col.append(principal)
    df["Interest"] = int_col
    df["Principal"] = prin_col
    df["Debt_Service"] = df["Interest"] + df["Principal"]

    # Taxes
    taxable = df["EBITDA"] + df["Interest"]  # interest deductible
    tax = [-max(0, v * a.tax_rate) if y > a.tax_holiday else 0 for y, v in taxable.items()]
    df["Tax"] = tax

    # Net cashflow to firm
    df["Net_CF_Firm"] = df[["EBITDA", "Interest", "Principal", "Tax"]].sum(axis=1)

    # Equity cashflows (include initial equity injection)
    debt_draw = -debt_nom  # positive at t=0
    df["Equity_CF"] = df["Net_CF_Firm"].copy()
    df.loc[0, "Equity_CF"] += df.loc[0, "Capex"] - debt_draw
    df.loc[0, "Equity_CF"] += df.loc[0, "Grants"]  # grants benefit equity

    # DSCR (avoid div/0)
    dscr = df["EBITDA"] / df["Debt_Service"].replace(0, np.nan)
    df["DSCR"] = dscr

    return df.fillna(0)


def compute_kpis(df: pd.DataFrame, a: Assumptions) -> Dict[str, float]:
    eq_cf = df["Equity_CF"].values.astype(float)
    irr = npf.irr(eq_cf)
    npv = npf.npv(a.discount_rate, eq_cf)

    # LCOE discounted (cost side only)
    disc = (1 / (1 + a.discount_rate) ** df.index).values
    cost = -(df["Capex"] + df["OPEX"]).values * disc
    energy = (df["Prod_MWh"] * 1_000).values * disc
    lcoe = cost.sum() / energy.sum() if energy.sum() else math.nan

    return {
        "IRR_equity": irr,
        "NPV_equity": npv,
        "LCOE_eur_kwh": lcoe,
        "Min_DSCR": df["DSCR"].replace([np.inf, -np.inf], np.nan).min(),
    }


def build_sensitivity(a: Assumptions, var: str, deltas: List[float] = (-0.1, -0.05, 0.05, 0.1)) -> pd.DataFrame:
    base_irr = compute_kpis(build_cashflow(a), a)["IRR_equity"]
    rows = []
    for d in deltas:
        a_mod = Assumptions(**asdict(a))
        setattr(a_mod, var, getattr(a_mod, var) * (1 + d))
        irr = compute_kpis(build_cashflow(a_mod), a_mod)["IRR_equity"]
        rows.append({"Scenario": f"{var} {d:+.0%}", "IRR_equity": irr, "Δ vs base": irr - base_irr})
    return pd.DataFrame(rows)


def export_excel(cf: pd.DataFrame, kpis: Dict[str, float], sens: pd.DataFrame, dest: Path):
    with pd.ExcelWriter(dest, engine="xlsxwriter") as writer:
        # Inputs
        pd.DataFrame(asdict(Assumptions()).items(), columns=["Assumption", "Value"]).to_excel(
            writer, sheet_name="Inputs", index=False
        )

        # Cashflow
        cf.to_excel(writer, sheet_name="Cashflow")

        # KPIs
        pd.DataFrame(kpis.items(), columns=["KPI", "Value"]).to_excel(writer, sheet_name="KPIs", index=False)

        # Sensitivity
        sens.to_excel(writer, sheet_name="Sensitivity", index=False)

        # Formatting (simple)
        workbook = writer.book
        fmt_pct = workbook.add_format({"num_format": "0.0%"})
        writer.sheets["KPIs"].set_column("B:B", 15)
        writer.sheets["Sensitivity"].set_column("B:C", None, fmt_pct)


def run_scenarios() -> Dict[str, Dict[str, float]]:
    """Run scenarios for both Ouarzazate and Dakhla, with and without MASEN/IRESEN incentives"""
    results = {}
    
    # Base case - Ouarzazate with incentives
    a_ouar = Assumptions(location="Ouarzazate")
    cf_ouar = build_cashflow(a_ouar)
    results["Ouarzazate"] = compute_kpis(cf_ouar, a_ouar)
    
    # Base case - Dakhla with incentives
    a_dakhla = Assumptions(location="Dakhla")
    cf_dakhla = build_cashflow(a_dakhla)
    results["Dakhla"] = compute_kpis(cf_dakhla, a_dakhla)
    
    # Stress case - Ouarzazate with incentives
    a_ouar_stress = Assumptions(
        location="Ouarzazate",
        production_mwh_ouarzazate=a_ouar.production_mwh_ouarzazate * 0.95,  # -5% irradiation
        capex_meur_ouarzazate=a_ouar.capex_meur_ouarzazate * 1.1,  # +10% CAPEX
        ppa_price_eur_kwh=a_ouar.ppa_price_eur_kwh * 0.9,  # -10% tariff
    )
    cf_ouar_stress = build_cashflow(a_ouar_stress)
    results["Ouarzazate_stress"] = compute_kpis(cf_ouar_stress, a_ouar_stress)
    
    # Stress case - Ouarzazate WITHOUT MASEN/IRESEN incentives
    a_ouar_stress_no_incent = Assumptions(
        location="Ouarzazate",
        production_mwh_ouarzazate=a_ouar.production_mwh_ouarzazate * 0.95,  # -5% irradiation
        capex_meur_ouarzazate=a_ouar.capex_meur_ouarzazate * 1.1,  # +10% CAPEX
        ppa_price_eur_kwh=a_ouar.ppa_price_eur_kwh * 0.9,  # -10% tariff
        grant_meur_masen=0.0,
        grant_meur_iresen=0.0,
        grant_meur_connection=0.0,
    )
    cf_ouar_stress_no_incent = build_cashflow(a_ouar_stress_no_incent)
    results["Ouarzazate_stress_no_incentives"] = compute_kpis(cf_ouar_stress_no_incent, a_ouar_stress_no_incent)
    
    return results

if __name__ == "__main__":
    a = Assumptions()
    cf = build_cashflow(a)
    kpis = compute_kpis(cf, a)
    sens = build_sensitivity(a, "ppa_price_eur_kwh")
    
    # Run and print comparative scenarios
    scenarios = run_scenarios()
    print("\nScenario Results:")
    for scenario, result in scenarios.items():
        irr = result["IRR_equity"]
        print(f"{scenario}: IRR = {irr*100:.1f}% {'(VIABLE)' if irr >= 0.10 else '(NOT VIABLE)'}")
    
    # Use a shorter path to avoid path length issues
    out = Path("solar_model.xlsx")
    export_excel(cf, kpis, sens, out)
    print("\nExcel model created:", out.resolve())
