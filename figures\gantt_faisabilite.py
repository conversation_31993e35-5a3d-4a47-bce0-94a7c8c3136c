"""gantt_faisabilite.py

Ce script génère un diagramme de Gantt illustrant le calendrier de développement 
et de construction du projet solaire 10 MW au Maroc, avec les principales étapes
et délais.

Le diagramme est exporté en PNG haute résolution dans le dossier figures.
"""

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import matplotlib.patches as mpatches
import matplotlib.dates as mdates
from matplotlib.dates import DateFormatter, MonthLocator, date2num

# Configuration
plt.rcParams.update({
    'font.family': 'sans-serif',
    'font.size': 10,
    'figure.figsize': (12, 7),
    'figure.dpi': 300,
})

# Couleurs
colors = {
    'phase1': '#3498db',  # Bleu - Études préliminaires
    'phase2': '#2ecc71',  # Vert - Autorisations
    'phase3': '#e74c3c',  # Rouge - Financement
    'phase4': '#f39c12',  # Orange - Construction
    'phase5': '#9b59b6',  # Violet - Mise en service
    'milestone': '#34495e',  # G<PERSON> foncé - Jalo<PERSON>
    'background': '#f8f9fa',  # Gris clair - Fond
    'grid': '#ecf0f1',  # Gris très clair - Grille
    'text': '#2c3e50',  # Bleu marine - Texte
}

# Date de début du projet
start_date = datetime(2025, 1, 1)

# Créer les tâches
tasks = [
    # Phase 1: Études préliminaires
    {"Task": "Étude de préfaisabilité", "Start": start_date, "Duration": 45, "Phase": "phase1"},
    {"Task": "Étude de ressource solaire", "Start": start_date + timedelta(days=15), "Duration": 90, "Phase": "phase1"},
    {"Task": "Étude d'impact environnemental", "Start": start_date + timedelta(days=45), "Duration": 75, "Phase": "phase1"},
    {"Task": "Étude technique détaillée", "Start": start_date + timedelta(days=60), "Duration": 90, "Phase": "phase1"},
    {"Task": "Étude de raccordement préliminaire", "Start": start_date + timedelta(days=30), "Duration": 60, "Phase": "phase1"},
    
    # Phase 2: Autorisations
    {"Task": "Demande autorisation provisoire", "Start": start_date + timedelta(days=120), "Duration": 45, "Phase": "phase2"},
    {"Task": "Instruction autorisation provisoire", "Start": start_date + timedelta(days=165), "Duration": 90, "Phase": "phase2"},
    {"Task": "Demande autorisation définitive", "Start": start_date + timedelta(days=270), "Duration": 30, "Phase": "phase2"},
    {"Task": "Instruction autorisation définitive", "Start": start_date + timedelta(days=300), "Duration": 60, "Phase": "phase2"},
    {"Task": "Permis de construire", "Start": start_date + timedelta(days=315), "Duration": 60, "Phase": "phase2"},
    {"Task": "Convention de raccordement", "Start": start_date + timedelta(days=330), "Duration": 45, "Phase": "phase2"},
    
    # Phase 3: Financement
    {"Task": "Dossier SIMEST", "Start": start_date + timedelta(days=150), "Duration": 60, "Phase": "phase3"},
    {"Task": "Instruction SIMEST", "Start": start_date + timedelta(days=210), "Duration": 120, "Phase": "phase3"},
    {"Task": "Dossier Charte Investissement", "Start": start_date + timedelta(days=180), "Duration": 45, "Phase": "phase3"},
    {"Task": "Instruction CRI", "Start": start_date + timedelta(days=225), "Duration": 90, "Phase": "phase3"},
    {"Task": "Due diligence bancaire", "Start": start_date + timedelta(days=270), "Duration": 75, "Phase": "phase3"},
    {"Task": "Closing financier", "Start": start_date + timedelta(days=345), "Duration": 30, "Phase": "phase3"},
    
    # Phase 4: Construction
    {"Task": "Appel d'offres EPC", "Start": start_date + timedelta(days=240), "Duration": 90, "Phase": "phase4"},
    {"Task": "Sélection EPC", "Start": start_date + timedelta(days=330), "Duration": 30, "Phase": "phase4"},
    {"Task": "Préparation site", "Start": start_date + timedelta(days=375), "Duration": 45, "Phase": "phase4"},
    {"Task": "Génie civil", "Start": start_date + timedelta(days=420), "Duration": 75, "Phase": "phase4"},
    {"Task": "Installation structures", "Start": start_date + timedelta(days=465), "Duration": 60, "Phase": "phase4"},
    {"Task": "Installation modules PV", "Start": start_date + timedelta(days=495), "Duration": 75, "Phase": "phase4"},
    {"Task": "Installation onduleurs", "Start": start_date + timedelta(days=525), "Duration": 45, "Phase": "phase4"},
    {"Task": "Installation poste livraison", "Start": start_date + timedelta(days=540), "Duration": 30, "Phase": "phase4"},
    {"Task": "Raccordement réseau", "Start": start_date + timedelta(days=570), "Duration": 30, "Phase": "phase4"},
    
    # Phase 5: Mise en service
    {"Task": "Tests préliminaires", "Start": start_date + timedelta(days=600), "Duration": 15, "Phase": "phase5"},
    {"Task": "Mise en service progressive", "Start": start_date + timedelta(days=615), "Duration": 30, "Phase": "phase5"},
    {"Task": "Tests performance", "Start": start_date + timedelta(days=645), "Duration": 15, "Phase": "phase5"},
    {"Task": "Réception provisoire", "Start": start_date + timedelta(days=660), "Duration": 15, "Phase": "phase5"},
    {"Task": "Exploitation commerciale", "Start": start_date + timedelta(days=675), "Duration": 30, "Phase": "phase5"},
]

# Jalons clés
milestones = [
    {"Task": "Validation préfaisabilité", "Date": start_date + timedelta(days=45)},
    {"Task": "Dépôt autorisation provisoire", "Date": start_date + timedelta(days=165)},
    {"Task": "Obtention autorisation provisoire", "Date": start_date + timedelta(days=255)},
    {"Task": "Obtention autorisation définitive", "Date": start_date + timedelta(days=360)},
    {"Task": "Bouclage financier", "Date": start_date + timedelta(days=375)},
    {"Task": "Début construction", "Date": start_date + timedelta(days=375)},
    {"Task": "Fin installation modules", "Date": start_date + timedelta(days=570)},
    {"Task": "Mise en service (COD)", "Date": start_date + timedelta(days=675)},
]

# Créer DataFrame
df_tasks = pd.DataFrame(tasks)

# Calculer end date
df_tasks['End'] = df_tasks.apply(lambda row: row['Start'] + timedelta(days=row['Duration']), axis=1)

# Créer la figure
fig, ax = plt.subplots(figsize=(12, 10))
ax.set_facecolor(colors['background'])

# Définir les limites de l'axe des dates
min_date = df_tasks['Start'].min() - timedelta(days=30)
max_date = df_tasks['End'].max() + timedelta(days=30)
ax.set_xlim(min_date, max_date)

# Format de l'axe des dates
ax.xaxis.set_major_locator(mdates.MonthLocator())
ax.xaxis.set_major_formatter(mdates.DateFormatter('%b %Y'))
plt.setp(ax.get_xticklabels(), rotation=45, ha='right')

# Grille
ax.grid(True, axis='x', linestyle='--', alpha=0.3, color=colors['grid'])
ax.set_axisbelow(True)

# Nombre de tâches
num_tasks = len(df_tasks)

# Position y pour chaque tâche
y_positions = {}
current_y = 0
for i, phase in enumerate(['phase1', 'phase2', 'phase3', 'phase4', 'phase5']):
    tasks_in_phase = df_tasks[df_tasks['Phase'] == phase]
    for j, (_, task) in enumerate(tasks_in_phase.iterrows()):
        y_positions[task['Task']] = current_y
        current_y += 1
    # Espace entre les phases
    current_y += 0.5

# Hauteur de la barre
bar_height = 0.6

# Tracer les barres pour chaque tâche
for _, task in df_tasks.iterrows():
    start_date = task['Start']
    end_date = task['End']
    y_pos = y_positions[task['Task']]
    
    # Barre de la tâche
    ax.barh(y_pos, (end_date - start_date).days, left=start_date, height=bar_height,
           color=colors[task['Phase']], alpha=0.8, edgecolor='white', linewidth=0.5)
    
    # Nom de la tâche
    ax.text(min_date - timedelta(days=10), y_pos, task['Task'], ha='right', va='center',
           fontsize=9, color=colors['text'])
    
    # Durée
    ax.text(end_date + timedelta(days=5), y_pos, f"{task['Duration']}j", 
           va='center', fontsize=8, color=colors['text'])

# Ajouter les jalons
for milestone in milestones:
    date = milestone['Date']
    # Trouver la tâche la plus proche de ce jalon
    closest_task = min(y_positions.items(), key=lambda x: abs(date - df_tasks[df_tasks['Task'] == x[0]]['End'].iloc[0]).days)
    y_pos = y_positions[closest_task[0]]
    
    # Symbole du jalon
    ax.scatter(date, y_pos, marker='D', s=80, color=colors['milestone'], zorder=5)
    
    # Étiquette du jalon (rotation pour éviter les chevauchements)
    ax.text(date, y_pos + 0.5, milestone['Task'], rotation=45, ha='right', va='bottom',
           fontsize=8, color=colors['milestone'], weight='bold')

# Définir les limites de l'axe y
ax.set_ylim(-1, current_y)

# Supprimer les étiquettes de l'axe y
ax.set_yticks([])

# Regroupement visuel des phases avec rectangles colorés de fond
phases = {
    'phase1': "Études préliminaires",
    'phase2': "Autorisations",
    'phase3': "Financement",
    'phase4': "Construction",
    'phase5': "Mise en service"
}

# Créer les rectangles de fond pour chaque phase
phase_y_ranges = {}
for phase in phases.keys():
    tasks_in_phase = df_tasks[df_tasks['Phase'] == phase]
    if not tasks_in_phase.empty:
        min_y = min(y_positions[task] for task in tasks_in_phase['Task'])
        max_y = max(y_positions[task] for task in tasks_in_phase['Task'])
        phase_y_ranges[phase] = (min_y - 0.3, max_y + 0.3)

# Dessiner les rectangles et étiquettes de phase
for phase, (y_min, y_max) in phase_y_ranges.items():
    rect = plt.Rectangle((min_date - timedelta(days=90), y_min), 
                       timedelta(days=60).total_seconds() / (24*3600), 
                       y_max - y_min, 
                       facecolor=colors[phase], alpha=0.3, edgecolor='none')
    ax.add_patch(rect)
    ax.text(min_date - timedelta(days=60), (y_min + y_max) / 2, 
           phases[phase], ha='center', va='center', rotation=90, 
           fontsize=10, weight='bold', color=colors[phase])

# Afficher progression temporelle
month_markers = pd.date_range(start=min_date, end=max_date, freq='3M')
for i, date in enumerate(month_markers):
    if i % 2 == 0:  # Tous les 6 mois
        ax.axvline(date, color='gray', linestyle='-', alpha=0.2, zorder=0)

# Titre et sous-titre
plt.suptitle("CALENDRIER DE DÉVELOPPEMENT ET CONSTRUCTION", 
           fontsize=14, weight='bold', color=colors['text'], y=0.98)
plt.title("Projet Solaire PV 10 MW - Maroc", 
        fontsize=12, color=colors['text'], style='italic', pad=10)

# Légende
handles = [mpatches.Patch(color=colors[phase], label=phases[phase], alpha=0.8) for phase in phases.keys()]
handles.append(mpatches.Patch(color=colors['milestone'], label="Jalons", alpha=0.8, marker='D', linestyle='None'))
plt.legend(handles=handles, loc='upper center', bbox_to_anchor=(0.5, -0.01), 
          ncol=len(phases) + 1, frameon=True, fancybox=True, shadow=True)

# Annotations
ax.text(0.01, 0.01, "Durée totale: 24 mois (675 jours)", transform=ax.transAxes,
       fontsize=9, style='italic', color=colors['text'])

# Ajuster les marges
plt.tight_layout(rect=[0.15, 0.03, 0.97, 0.95])

# Sauvegarder le diagramme
plt.savefig('figures/gantt_faisabilite.png', dpi=300, bbox_inches='tight', 
           facecolor=colors['background'])
print("Diagramme de Gantt généré avec succès!")
plt.close()
