# Mémoire de Master sur les Investissements Italiens au Maroc dans le Cadre du Piano Mattei

Ce dépôt contient le code source LaTeX pour le mémoire de fin d'études intitulé "Fondements Stratégiques et Juridiques des Investissements Italiens au Maroc dans le Cadre du Piano Mattei".

## Structure du projet

Le projet est organisé comme suit :

```
latex_project/
├── main.tex                 # Fichier principal qui importe tous les autres fichiers
├── preliminaries/           # Pages préliminaires (page de garde, dédicace, remerciements, etc.)
├── introduction/            # Introduction générale
├── partie1/                 # Première partie du mémoire
├── partie2/                 # Seconde partie du mémoire
├── conclusion/              # Conclusion générale
├── bibliography/            # Bibliographie
└── annexes/                 # Annexes
```

## Prérequis

Pour compiler ce projet LaTeX, vous aurez besoin de :

- Une distribution LaTeX complète (TeXLive, MiKTeX, ou MacTeX)
- Un éditeur LaTeX (TeXstudio, Overleaf, VS Code avec l'extension LaTeX Workshop, etc.)

## Comment compiler

### Avec un éditeur LaTeX

1. Ouvrez le fichier `main.tex` dans votre éditeur LaTeX
2. Assurez-vous que votre éditeur est configuré pour utiliser `pdflatex` et `bibtex`
3. Compilez le document (généralement avec F5 ou Ctrl+T dans la plupart des éditeurs)

### En ligne de commande

Pour générer le PDF, exécutez les commandes suivantes dans le répertoire du projet :

```bash
pdflatex main.tex
bibtex main
pdflatex main.tex
pdflatex main.tex
```

Les multiples exécutions de `pdflatex` sont nécessaires pour résoudre toutes les références croisées et les citations.

## Personnalisation

- **Page de garde** : Modifiez le fichier `preliminaries/page_de_garde.tex` pour mettre à jour les informations personnelles et institutionnelles.
- **Logo** : Remplacez le fichier `preliminaries/logo_university.png` par le logo de votre université.
- **Bibliographie** : Ajoutez vos références dans le fichier `bibliography/references.bib`.

## Problèmes courants

- Si vous rencontrez des erreurs liées aux packages manquants, installez-les via le gestionnaire de packages de votre distribution LaTeX.
- Pour les problèmes de compilation liés à la bibliographie, assurez-vous d'exécuter `bibtex` après la première compilation avec `pdflatex`.

## Contact

Pour toute question ou suggestion, veuillez contacter [<EMAIL>]. 