#!/usr/bin/env python3
"""
Test script for enhanced financial model application
"""
import sys
from pathlib import Path

def test_import():
    """Test that all modules can be imported"""
    try:
        print("Testing imports...")
        
        # Test core module imports
        from core.enhanced_financial_model import EnhancedAssumptions, build_enhanced_cashflow, compute_enhanced_kpis
        print("✓ Core financial model imports successful")
        
        from core.enhanced_data_models import EnhancedProjectAssumptions
        print("✓ Enhanced data models import successful")
        
        from core.model_validation import validate_model_comprehensive
        print("✓ Model validation import successful")
        
        # Test main app import
        import enhanced_main
        print("✓ Enhanced main app import successful")
        
        return True
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False
    except Exception as e:
        print(f"✗ Unexpected error: {e}")
        return False

def test_basic_functionality():
    """Test basic functionality without UI"""
    try:
        print("\nTesting basic functionality...")
        
        # Create test assumptions
        from core.enhanced_data_models import EnhancedProjectAssumptions
        assumptions = EnhancedProjectAssumptions()
        print(f"✓ Created assumptions for {assumptions.location_name}")
        
        # Convert to enhanced format
        from core.enhanced_financial_model import EnhancedAssumptions, build_enhanced_cashflow, compute_enhanced_kpis
        
        assumptions_dict = assumptions.to_dict()
        if 'project_life_years' in assumptions_dict:
            assumptions_dict['years'] = assumptions_dict.pop('project_life_years')
        
        # Add missing defaults
        defaults = {
            'grant_meur_iresen': 0.0,
            'use_terminal_value': True,
            'terminal_growth_rate': 0.025,
            'working_capital_days': 30,
            'corporate_tax_rate': 0.31,
            'tax_holiday_years': 5,
            'insurance_rate': 0.003,
            'land_lease_eur_mw_year': 2000,
            'opex_escalation': 0.025,
            'grace_years': 2
        }
        
        for key, value in defaults.items():
            if key not in assumptions_dict:
                assumptions_dict[key] = value
        
        # Filter valid parameters
        from core.enhanced_financial_model import EnhancedAssumptions
        valid_params = {
            k: v for k, v in assumptions_dict.items() 
            if k in EnhancedAssumptions.__dataclass_fields__
        }
        
        enhanced_assumptions = EnhancedAssumptions(**valid_params)
        print("✓ Created enhanced assumptions")
        
        # Run financial model
        cashflow = build_enhanced_cashflow(enhanced_assumptions)
        print(f"✓ Built cashflow model with {len(cashflow)} years")
        
        kpis = compute_enhanced_kpis(cashflow, enhanced_assumptions)
        print(f"✓ Computed KPIs: IRR={kpis.get('IRR_project', 0):.1%}, LCOE={kpis.get('LCOE_eur_kwh', 0):.3f} EUR/kWh")
        
        # Test validation
        from core.model_validation import validate_model_comprehensive
        validation_results = validate_model_comprehensive(assumptions, kpis, cashflow)
        print(f"✓ Validation completed: {'Valid' if validation_results.is_valid else 'Issues found'}")
        
        return True
        
    except Exception as e:
        print(f"✗ Functionality test error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("Enhanced Financial Model Application - Test Suite")
    print("=" * 50)
    
    # Test imports
    import_success = test_import()
    
    if import_success:
        # Test basic functionality
        functionality_success = test_basic_functionality()
        
        if functionality_success:
            print("\n" + "=" * 50)
            print("✓ All tests passed! The enhanced application is ready to use.")
            print("\nTo run the application:")
            print("python enhanced_main.py")
            return True
        else:
            print("\n" + "=" * 50)
            print("✗ Functionality tests failed.")
            return False
    else:
        print("\n" + "=" * 50)
        print("✗ Import tests failed. Please check dependencies.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 