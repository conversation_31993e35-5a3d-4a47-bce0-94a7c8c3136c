\documentclass[crop,tikz]{standalone}
\usepackage{tikz}
\usepackage{xcolor}
\usepackage[utf8]{inputenc}
\usetikzlibrary{shapes,arrows,positioning,fit,backgrounds,patterns,decorations.pathreplacing,calc}

% Define colors
\definecolor{maincolor}{RGB}{0,83,135}
\definecolor{pillarcolor}{RGB}{0,127,175}
\definecolor{boxcolor}{RGB}{240,240,240}
\definecolor{boxborder}{RGB}{200,200,200}
\definecolor{italyflag}{RGB}{0,146,70}
\definecolor{italyflagred}{RGB}{206,43,55}
\definecolor{africacolor}{RGB}{222,128,0}

\begin{document}
\begin{tikzpicture}[
    node distance=1cm,
    maintitle/.style={font=\Large\bfseries, text=white, align=center},
    subtitle/.style={font=\normalsize, text=white, align=center},
    pillar/.style={draw=pillarcolor, fill=pillarcolor, text=white, rounded corners, font=\bfseries, text width=2.5cm, align=center, minimum height=1cm},
    country/.style={draw=boxborder, fill=boxcolor, rounded corners, text width=2cm, align=center, font=\small},
    arrow/.style={->, >=latex, thick, color=maincolor},
    phase/.style={draw=boxborder, fill=boxcolor, rounded corners, text width=3.8cm, align=center, font=\small},
    flag/.style={font=\small, align=center}
]

% Main title box
\node[draw=maincolor, fill=maincolor, rounded corners, minimum width=15cm, minimum height=1.5cm] (title) {};
\node[maintitle] at (title) {PIANO MATTEI PER L'AFRICA};
\node[subtitle] at ($(title.south)+(0,0.25)$) {Nouvelle stratégie de coopération Italie-Afrique (2024-2028)};

% Approach box - moved above title for better layout
\node[draw=italyflag, fill=white, text=italyflag, rounded corners, above=0.4cm of title, minimum width=14cm] (approach) 
{\textbf{APPROCHE NON-PRÉDATRICE} $\cdot$ \textbf{PARTENARIAT D'ÉGAL À ÉGAL} $\cdot$ \textbf{DÉVELOPPEMENT DURABLE}};

% Pillars
\node[pillar, below=1cm of title] (pillar1) {ÉDUCATION \& FOR-\\MATION};
\node[pillar, right=0.5cm of pillar1] (pillar2) {SANTÉ};
\node[pillar, right=0.5cm of pillar2] (pillar3) {AGRICULTURE};
\node[pillar, right=0.5cm of pillar3] (pillar4) {EAU};
\node[pillar, right=0.5cm of pillar4] (pillar5) {ÉNERGIE};

% Phase boxes
\node[phase, below=1.5cm of pillar1] (phase1) {\textbf{PHASE 1 : IDEN-\\TIFICATION}\\Analyse des besoins et des opportunités dans chaque pays pilote};

\node[phase, below=1.5cm of pillar3] (phase2) {\textbf{PHASE 2 : FI-\\NANCEMENT}\\5,5 milliards € (prêts, dons et garanties)};

\node[phase, below=1.5cm of pillar5] (phase3) {\textbf{PHASE 3 : EXÉCUTION}\\Partenariats public-privé et approche multipartite};

\node[phase, below=1cm of phase2] (phase4) {\textbf{PHASE 4 : ÉVALUATION}\\Suivi des impacts et ajustements};

% Countries box
\node[draw=africacolor, fill=white, rounded corners, text=africacolor, below=4.5cm of pillar3, text width=14cm, align=center] (countries) {
\textbf{PAYS PILOTES :} Algérie $\cdot$ Maroc $\cdot$ Tunisie $\cdot$ Égypte $\cdot$ Éthiopie $\cdot$ Ke-\\nya $\cdot$ 
Côte d'Ivoire $\cdot$ République Démocratique du Congo $\cdot$ Mozambique
};

% Governance box
\node[draw=maincolor, fill=white, text=maincolor, rounded corners, below=0.7cm of countries, text width=14cm, align=center] (governance) {
\textbf{GOUVERNANCE :} Comité de pilotage (Cabinet du Pre-\\mier ministre) $\cdot$ Ministères $\cdot$ Agences italiennes $\cdot$ 
Autorités locales africaines $\cdot$ Secteur privé $\cdot$ Société civile
};

% Arrows connecting sections
\draw[arrow] ($(title.south)-(5,0)$) -- ($(pillar1.north)-(0,0.1)$);
\draw[arrow] ($(title.south)-(2.5,0)$) -- ($(pillar2.north)-(0,0.1)$);
\draw[arrow] ($(title.south)-(0,0)$) -- ($(pillar3.north)-(0,0.1)$);
\draw[arrow] ($(title.south)+(2.5,0)$) -- ($(pillar4.north)-(0,0.1)$);
\draw[arrow] ($(title.south)+(5,0)$) -- ($(pillar5.north)-(0,0.1)$);

\draw[arrow] (pillar1.south) -- (phase1.north);
\draw[arrow] (pillar3.south) -- (phase2.north);
\draw[arrow] (pillar5.south) -- (phase3.north);

\draw[arrow] (phase1.south) -- ($(countries.north)-(4,0)$);
\draw[arrow] (phase2.south) -- ($(countries.north)+(0,0)$);
\draw[arrow] (phase3.south) -- ($(countries.north)+(4,0)$);
\draw[arrow] (phase4.south) -- ($(countries.north)+(0,0)$);

\draw[arrow] (countries) -- (governance);

% Coordinates for the Italy flag (avoiding nested tikzpicture)
\coordinate (italyflag_pos) at ($(governance.south) + (-5.5,-0.7)$);

% Draw Italian flag directly
\fill[italyflag] ($(italyflag_pos)+(-1.5,1)$) rectangle ($(italyflag_pos)+(-0.5,2.5)$);
\fill[white] ($(italyflag_pos)+(-0.5,1)$) rectangle ($(italyflag_pos)+(0.5,2.5)$);
\fill[italyflagred] ($(italyflag_pos)+(0.5,1)$) rectangle ($(italyflag_pos)+(1.5,2.5)$);

% Italy text
\node[font=\small\bfseries, align=center] at ($(italyflag_pos)+(0,0.5)$) {ITALIA};

% Coordinates for Africa map (avoiding nested tikzpicture)
\coordinate (africaflag_pos) at ($(governance.south) + (5.5,-0.7)$);

% Draw simplified Africa map
\filldraw[fill=africacolor, draw=africacolor!80!black] 
  ($(africaflag_pos)+(-1,1)$) to[out=80,in=180] ($(africaflag_pos)+(0,2)$) to[out=0,in=90] ($(africaflag_pos)+(1,1.5)$) 
  to[out=270,in=0] ($(africaflag_pos)+(0.5,0.5)$) to[out=180,in=270] ($(africaflag_pos)+(-1,1)$);

% Africa text
\node[font=\small\bfseries, align=center] at ($(africaflag_pos)+(0,0.5)$) {AFRICA};

% Two-headed arrow connecting flags
\draw[->, >=latex, thick, color=maincolor] ($(italyflag_pos)+(0,0.7)$) -- ($(governance.south)-(2,0)$);
\draw[<-, >=latex, thick, color=maincolor] ($(africaflag_pos)+(0,0.7)$) -- ($(governance.south)+(2,0)$);

\end{tikzpicture}
\end{document} 