% Define improved colors with better contrast
\definecolor{mainblue}{RGB}{0,90,150}         % Main blue for title - slightly darker for better printing
\definecolor{pillarsblue}{RGB}{41,128,185}    % Clearer blue for pillars
\definecolor{boxgray}{RGB}{245,245,245}       % Light gray for boxes
\definecolor{italygreen}{RGB}{0,140,69}       % Italian flag green
\definecolor{italyred}{RGB}{205,33,42}        % Italian flag red
\definecolor{africagold}{RGB}{212,175,55}     % Richer gold for Africa

\begin{tikzpicture}[scale=1.0]
    % Define improved styles for A4 printing
    \tikzstyle{title}=[draw=mainblue, fill=mainblue, text=white, font=\Large\bfseries, text width=10cm, align=center, rounded corners=3pt, drop shadow={shadow xshift=0.5mm, shadow yshift=-0.5mm, opacity=0.3}]
    \tikzstyle{subtitle}=[font=\normalsize, text=gray!70, align=center]
    \tikzstyle{pillar}=[draw=pillarsblue, fill=pillarsblue, text=white, font=\bfseries, text width=2.6cm, align=center, rounded corners=3pt, minimum height=1.2cm, drop shadow={shadow xshift=0.3mm, shadow yshift=-0.3mm, opacity=0.2}]
    \tikzstyle{phase}=[draw=gray!40, fill=boxgray, text width=3.8cm, align=center, font=\small, rounded corners=3pt, minimum height=2.5cm]
    \tikzstyle{countries}=[draw=gray!40, fill=boxgray, text width=16cm, align=center, font=\small, rounded corners=3pt]
    \tikzstyle{governance}=[draw=gray!40, fill=boxgray, text width=16cm, align=center, font=\small, rounded corners=3pt]
    \tikzstyle{arrow}=[thick, ->, >=Stealth, color=mainblue]
    \tikzstyle{frame}=[draw=mainblue, rounded corners=4pt, line width=1pt]
    
    % Outer frame with better proportions for A4
    \draw[frame] (-9.5,8) rectangle (9.5,-9);    
    
    % Core principles at the top - better positioned
    \node[align=center, text=mainblue, font=\bfseries] at (0,7.2) {APPROCHE NON-PRÉDATRICE \textbullet\ PARTENARIAT D'ÉGAL À ÉGAL \textbullet\ DÉVELOPPEMENT DURABLE};

    % Main title with subtle shadow for depth
    \node[title] (title) at (0,5.8) {PIANO MATTEI PER L'AFRICA};
    \node[subtitle, below=0.15cm of title] {Nouvel outil de coopération Italo-Africaine (2024-2027)};

    % The five pillars/sectors - better spaced
    \node[pillar] (education) at (-6.5,3) {ÉDUCATION \& FORMATION};
    \node[pillar] (sante) at (-3.25,3) {SANTÉ};
    \node[pillar] (agriculture) at (0,3) {AGRICULTURE};
    \node[pillar] (eau) at (3.25,3) {EAU};
    \node[pillar] (energie) at (6.5,3) {ÉNERGIE};

    % Connect pillars to title with smooth arrows
    \foreach \pillar in {education,sante,agriculture,eau,energie}
        \draw[arrow] (title) -- (\pillar);

    % The three phases with clear structure and spacing
    \node[phase] (phase1) at (-5,0) {\textbf{PHASE 1 : IDENTIFICATION}\\[0.2cm]  
    Analyse des besoins et opportunités\\des projets dans les différents\\secteurs prioritaires};

    \node[phase] (phase2) at (0,0) {\textbf{PHASE 2 : FINANCEMENT}\\[0.2cm] 
    5,5 milliards €\\(prêts, dons et garanties)\\fonds publics et privés};

    \node[phase] (phase3) at (5,0) {\textbf{PHASE 3 : EXÉCUTION}\\[0.2cm] 
    Partenariats public-privé\\et coordination des acteurs\\pour mise en œuvre};

    % Connecting arrows for the phases - better distributed
    \draw[arrow] (education) -- (phase1);
    \draw[arrow] (sante) -- (phase1);
    \draw[arrow] (agriculture) -- (phase2);
    \draw[arrow] (eau) -- (phase3);
    \draw[arrow] (energie) -- (phase3);

    % Countries list with clearer formatting
    \node[countries] (countries) at (0,-3.5) {\textbf{PAYS PILOTES}\\[0.2cm]
    \begin{tabular}{ccccc}
    Algérie & Maroc & Tunisie & Égypte & Éthiopie \\
    Kenya & Congo & Rép. Dém. du Congo & Mozambique &
    \end{tabular}};

    % Governance structure with better organization
    \node[governance] (governance) at (0,-6) {\textbf{STRUCTURE DE GOUVERNANCE}\\[0.2cm]
    \begin{tabular}{ccccc}
    Comité de pilotage & Ministères & Institutions financières & Entreprises & Société civile \\
    (Cabinet du Premier ministre) & & (CDP, BEI) & publiques et privées & organisations
    \end{tabular}};

    % Connect governance to countries
    \draw[arrow] (countries) -- (governance);
    
    % Horizontal connector between phases
    \draw[arrow, dashed] (phase1) -- (phase2);
    \draw[arrow, dashed] (phase2) -- (phase3);
    
    % Connect phases to countries
    \draw[arrow] (phase2) -- (0,-2) -- (0,-3.5);

    % Italy and Africa flags with actual colors and better positioning
    % Italian flag - improved
    \begin{scope}[shift={(-3,-8)}]
        \draw[line width=0.5pt, black!30] (-0.75,0.45) rectangle (0.75,-0.45);
        \fill[italygreen] (-0.75,0.45) rectangle (-0.25,-0.45);
        \fill[white] (-0.25,0.45) rectangle (0.25,-0.45);
        \fill[italyred] (0.25,0.45) rectangle (0.75,-0.45);
        \node[font=\bfseries, text=mainblue] at (0,-0.85) {ITALIA};
    \end{scope} 
    
    % African Union symbol - simplified representation
    \begin{scope}[shift={(3,-8)}]
        \draw[line width=0.5pt, black!30] (-0.75,0.45) rectangle (0.75,-0.45);
        \fill[africagold] (-0.75,0.45) rectangle (0.75,-0.45);
        \node[font=\bfseries, text=mainblue] at (0,-0.85) {AFRICA};
    \end{scope}
    
    % Add footnote
    \node[font=\footnotesize, text=gray, align=right] at (8.5,-8.5) {Document préparé le \today};
\end{tikzpicture}