import flet as ft
from flet.matplotlib_chart import MatplotlibChart
import matplotlib
matplotlib.use('agg')
# Explicit imports for PyInstaller compatibility
import matplotlib.backends.backend_agg
import matplotlib.backends.backend_svg
import matplotlib.pyplot as plt
import pandas as pd
from pathlib import Path
import math

from core.data_models import Assumptions
from core.financial_model_logic import build_cashflow, compute_kpis, build_sensitivity, export_excel as export_excel_logic
from core.figure_generator_logic import (
    generate_fcfe_cumule_fig,
    generate_dscr_fig,
    generate_sens_tri_fig,
    generate_structure_financement_pie_fig,
    generate_scenario_comparison_fig,
    generate_gantt_chart_fig,
    generate_lcoe_waterfall_fig
)

# Moroccan renewable energy locations based on existing and planned projects
DEFAULT_PROFILES = {
    "Ouarzazate": {
        "production_mwh_year1": 18_000, "opex_keuros_year1": 285, "capex_meur": 9.5,
        "grant_meur_masen": 0.95, "grant_meur_iresen": 0.475, "grant_meur_connection": 0.285, "grant_meur_italy": 0.95,
        "description": "Noor Ouarzazate Solar Complex - World's largest solar complex (580MW)"
    },
    "Dakhla": {
        "production_mwh_year1": 19_200, "opex_keuros_year1": 310, "capex_meur": 9.8,
        "grant_meur_masen": 0.0, "grant_meur_iresen": 0.475, "grant_meur_connection": 0.300, "grant_meur_italy": 0.95,
        "description": "Dakhla region - High wind speeds (7-8.5 m/s) and excellent solar irradiation"
    },
    "Laayoune": {
        "production_mwh_year1": 17_800, "opex_keuros_year1": 295, "capex_meur": 9.2,
        "grant_meur_masen": 0.5, "grant_meur_iresen": 0.475, "grant_meur_connection": 0.320, "grant_meur_italy": 0.95,
        "description": "Laayoune - Part of Morocco's solar plan with wind farm projects"
    },
    "Midelt": {
        "production_mwh_year1": 16_500, "opex_keuros_year1": 270, "capex_meur": 8.8,
        "grant_meur_masen": 0.7, "grant_meur_iresen": 0.475, "grant_meur_connection": 0.250, "grant_meur_italy": 0.95,
        "description": "Midelt - 400km east of Casablanca, part of 850MW integrated wind project"
    },
    "Tarfaya": {
        "production_mwh_year1": 18_500, "opex_keuros_year1": 300, "capex_meur": 9.3,
        "grant_meur_masen": 0.6, "grant_meur_iresen": 0.475, "grant_meur_connection": 0.280, "grant_meur_italy": 0.95,
        "description": "Tarfaya - 300MW wind farm, one of the largest in Africa (45% load factor)"
    },
    "Boujdour": {
        "production_mwh_year1": 17_200, "opex_keuros_year1": 285, "capex_meur": 8.9,
        "grant_meur_masen": 0.4, "grant_meur_iresen": 0.475, "grant_meur_connection": 0.300, "grant_meur_italy": 0.95,
        "description": "Boujdour - Western Sahara region, part of Morocco's renewable energy expansion"
    },
    "Essaouira": {
        "production_mwh_year1": 16_800, "opex_keuros_year1": 275, "capex_meur": 8.7,
        "grant_meur_masen": 0.5, "grant_meur_iresen": 0.475, "grant_meur_connection": 0.260, "grant_meur_italy": 0.95,
        "description": "Essaouira - Atlantic coast, excellent wind resources (7-8.5 m/s)"
    },
    "Tangier": {
        "production_mwh_year1": 15_500, "opex_keuros_year1": 260, "capex_meur": 8.5,
        "grant_meur_masen": 0.6, "grant_meur_iresen": 0.475, "grant_meur_connection": 0.240, "grant_meur_italy": 0.95,
        "description": "Tangier - Northern Morocco, high wind speeds (8-11 m/s), close to Europe"
    },
    "Ain Beni Mathar": {
        "production_mwh_year1": 16_200, "opex_keuros_year1": 265, "capex_meur": 8.6,
        "grant_meur_masen": 0.8, "grant_meur_iresen": 0.475, "grant_meur_connection": 0.230, "grant_meur_italy": 0.95,
        "description": "Ain Beni Mathar - Central Morocco, thermo-solar facility location"
    },
    "Custom/Manual": { # User-defined location
        "production_mwh_year1": 10_000, "opex_keuros_year1": 200, "capex_meur": 8.0,
        "grant_meur_masen": 0.0, "grant_meur_iresen": 0.0, "grant_meur_connection": 0.0, "grant_meur_italy": 0.0,
        "description": "Custom location - Enter your own parameters for any location in Morocco"
    }
}

initial_profile_data = DEFAULT_PROFILES["Ouarzazate"]
current_assumptions = Assumptions(
    location_name="Ouarzazate",
    production_mwh_year1=initial_profile_data["production_mwh_year1"],
    opex_keuros_year1=initial_profile_data["opex_keuros_year1"],
    capex_meur=initial_profile_data["capex_meur"],
    grant_meur_masen=initial_profile_data["grant_meur_masen"],
    grant_meur_iresen=initial_profile_data["grant_meur_iresen"],
    grant_meur_connection=initial_profile_data["grant_meur_connection"],
    grant_meur_italy=initial_profile_data["grant_meur_italy"]
)

latest_cash_flow_df = None
latest_kpis = None
latest_sensitivity_df = None
current_fig_generator = None
current_fig_args = ()
current_fig_title = ""
selected_output_path = None

# --- Global UI Element References (assigned in main) ---
page_ref: ft.Page = None
selected_folder_text_ref: ft.Text = None
kpi_display_ref: ft.Text = None
chart_display: MatplotlibChart = None
location_profile_dropdown: ft.Dropdown = None
ppa_price_input: ft.TextField = None
prod_input: ft.TextField = None
opex_input: ft.TextField = None
capex_input: ft.TextField = None
grant_masen_input: ft.TextField = None
grant_iresen_input: ft.TextField = None
grant_connection_input: ft.TextField = None
grant_italy_input: ft.TextField = None
years_input: ft.TextField = None
degradation_input: ft.TextField = None
price_escalation_input: ft.TextField = None
opex_escalation_input: ft.TextField = None
debt_ratio_input: ft.TextField = None
interest_rate_input: ft.TextField = None
debt_years_input: ft.TextField = None
grace_years_input: ft.TextField = None
tax_holiday_input: ft.TextField = None
tax_rate_input: ft.TextField = None
discount_rate_input: ft.TextField = None

all_input_text_fields_list = [] # Populated in main()

def update_assumptions_and_run(e):
    global current_assumptions, latest_cash_flow_df, latest_kpis, latest_sensitivity_df
    
    parsing_errors_found = False

    def get_val(tf: ft.TextField, name: str, is_int=False, is_percentage=False):
        nonlocal parsing_errors_found
        tf.error_text = "" # Clear previous error
        value_str = tf.value.strip()
        if not value_str:
            tf.error_text = f"{name} is required."
            parsing_errors_found = True
            return None
        try:
            val = int(value_str) if is_int else float(value_str)
            if is_percentage:
                val /= 100.0
            return val
        except ValueError:
            tf.error_text = f"Invalid { 'integer' if is_int else 'number' } for {name}."
            parsing_errors_found = True
            return None

    for field_tf in all_input_text_fields_list: # Clear all errors first
        if field_tf: field_tf.error_text = ""

    temp_assumptions = {}
    # Get location name from dropdown or custom input
    location_name = location_profile_dropdown.value if location_profile_dropdown else "Ouarzazate"
    if location_name == "Custom/Manual" and custom_location_input and custom_location_input.value.strip():
        location_name = custom_location_input.value.strip()
    temp_assumptions['location_name'] = location_name
    temp_assumptions['ppa_price_eur_kwh'] = get_val(ppa_price_input, "PPA Price")
    temp_assumptions['production_mwh_year1'] = get_val(prod_input, "Production")
    temp_assumptions['opex_keuros_year1'] = get_val(opex_input, "OPEX")
    temp_assumptions['capex_meur'] = get_val(capex_input, "CAPEX")
    temp_assumptions['grant_meur_italy'] = get_val(grant_italy_input, "Grant Italy")
    temp_assumptions['grant_meur_masen'] = get_val(grant_masen_input, "Grant MASEN")
    temp_assumptions['grant_meur_iresen'] = get_val(grant_iresen_input, "Grant IRESEN")
    temp_assumptions['grant_meur_connection'] = get_val(grant_connection_input, "Grant Connection")
    temp_assumptions['years'] = get_val(years_input, "Project Years", is_int=True)
    temp_assumptions['degradation'] = get_val(degradation_input, "Degradation", is_percentage=True)
    temp_assumptions['price_escalation'] = get_val(price_escalation_input, "PPA Escal.", is_percentage=True)
    temp_assumptions['opex_escalation'] = get_val(opex_escalation_input, "OPEX Escal.", is_percentage=True)
    temp_assumptions['debt_ratio'] = get_val(debt_ratio_input, "Debt Ratio", is_percentage=True)
    temp_assumptions['interest_rate'] = get_val(interest_rate_input, "Interest Rate", is_percentage=True)
    temp_assumptions['debt_years'] = get_val(debt_years_input, "Debt Years", is_int=True)
    temp_assumptions['grace_years'] = get_val(grace_years_input, "Grace Years", is_int=True)
    temp_assumptions['tax_holiday'] = get_val(tax_holiday_input, "Tax Holiday", is_int=True)
    temp_assumptions['tax_rate'] = get_val(tax_rate_input, "Tax Rate", is_percentage=True)
    temp_assumptions['discount_rate'] = get_val(discount_rate_input, "Discount Rate", is_percentage=True)

    if parsing_errors_found:
        kpi_display_ref.value = "Please correct errors in the input fields."
        kpi_display_ref.color = ft.Colors.RED
        page_ref.update(*all_input_text_fields_list, kpi_display_ref)
        return

    try:
        # Debug: Print the temp_assumptions to see what values we're using
        print(f"DEBUG - Creating assumptions with: years={temp_assumptions.get('years')}, location={temp_assumptions.get('location_name')}")
        current_assumptions = Assumptions(**temp_assumptions)
        print(f"DEBUG - Created assumptions: years={current_assumptions.years}, location={current_assumptions.location_name}")
    except Exception as ex_init: # Catch potential errors during Assumptions instantiation
        kpi_display_ref.value = f"Error initializing assumptions: {ex_init}"
        kpi_display_ref.color = ft.Colors.RED
        page_ref.update(kpi_display_ref)
        return
        
    try:
        cash_flow_df = build_cashflow(current_assumptions)
        kpis_result = compute_kpis(cash_flow_df, current_assumptions)
        sensitivity_df_result = build_sensitivity(current_assumptions, "ppa_price_eur_kwh")

        latest_cash_flow_df = cash_flow_df
        latest_kpis = kpis_result
        latest_sensitivity_df = sensitivity_df_result
        
        kpi_list = [f"{key}: {value:.2f}" if isinstance(value, float) and not (math.isinf(value) or math.isnan(value)) else f"{key}: {str(value)}" for key, value in kpis_result.items()]
        kpi_display_ref.value = f"KPIs ({current_assumptions.location_name}):\n" + "\n".join(kpi_list)
        kpi_display_ref.color = None 
        
        show_fcfe_chart(None) # This also calls page_ref.update()
    except Exception as ex_calc:
        kpi_display_ref.value = f"Error during calculation: {ex_calc}"
        kpi_display_ref.color = ft.Colors.RED
        latest_cash_flow_df = None; latest_kpis = None; latest_sensitivity_df = None
        global current_fig_generator, current_fig_args, current_fig_title
        current_fig_generator = None; current_fig_args = (); current_fig_title = ""
        page_ref.update(kpi_display_ref)

def show_chart(fig_generator_func, title="Chart", *args):
    global current_fig_generator, current_fig_args, current_fig_title
    plt.close('all')
    if page_ref: page_ref.splash = ft.ProgressBar(); page_ref.update()
    try:
        fig = fig_generator_func(*args)
        if fig and chart_display:
            chart_display.figure = fig
            chart_display.update() 
            if kpi_display_ref: kpi_display_ref.value = f"Displaying: {title}" 
            current_fig_generator, current_fig_args, current_fig_title = fig_generator_func, args, title
        elif kpi_display_ref:
            kpi_display_ref.value = f"Could not generate: {title}"
            current_fig_generator, current_fig_args, current_fig_title = None, (), ""
    except Exception as ex:
        if kpi_display_ref: kpi_display_ref.value = f"Error generating {title}: {ex}"
        current_fig_generator, current_fig_args, current_fig_title = None, (), ""
    if page_ref: page_ref.splash = None; page_ref.update()

def get_cashflow_and_sensitivity():
    cf_df = build_cashflow(current_assumptions)
    sens_df = build_sensitivity(current_assumptions, "ppa_price_eur_kwh")
    return cf_df, sens_df

def show_fcfe_chart(e): cf_df, _ = get_cashflow_and_sensitivity(); show_chart(generate_fcfe_cumule_fig, "FCFE Cumulé", cf_df)
def show_dscr_chart(e): cf_df, _ = get_cashflow_and_sensitivity(); show_chart(generate_dscr_fig, "DSCR", cf_df)
def show_sens_tri_chart(e): _, sens_df = get_cashflow_and_sensitivity(); show_chart(generate_sens_tri_fig, "Sensibilité TRI", sens_df)
def show_structure_financement_chart(e): cf_df, _ = get_cashflow_and_sensitivity(); show_chart(generate_structure_financement_pie_fig, "Structure Financement", cf_df)
def show_scenario_comparison_chart(e): 
    # Show popup for dual-location comparison
    show_comparison_popup()

def show_comparison_popup():
    """Show popup for dual-location comparison setup."""
    global page
    
    # Create dropdown for second location
    second_location_dropdown = ft.Dropdown(
        label="Sélectionner la 2ème localisation",
        options=[
            ft.dropdown.Option("Ouarzazate", "Ouarzazate"),
            ft.dropdown.Option("Tarfaya", "Tarfaya"),
            ft.dropdown.Option("Dakhla", "Dakhla"),
            ft.dropdown.Option("Noor Midelt", "Noor Midelt"),
            ft.dropdown.Option("Laâyoune", "Laâyoune"),
            ft.dropdown.Option("Tan-Tan", "Tan-Tan"),
            ft.dropdown.Option("Boujdour", "Boujdour"),
            ft.dropdown.Option("Tata", "Tata"),
            ft.dropdown.Option("Zagora", "Zagora"),
            ft.dropdown.Option("Custom", "Custom")
        ],
        value="Dakhla",
        width=300
    )
    
    # Custom data inputs (initially hidden)
    # Basic parameters with styling
    custom_production_input = ft.TextField(
        label="Production (MWh/an)",
        value="18000",
        width=140,
        visible=False,
        bgcolor=ft.Colors.BLUE_GREY_50,
        border_color=ft.Colors.BLUE_400,
        focused_border_color=ft.Colors.BLUE_600,
        label_style=ft.TextStyle(color=ft.Colors.BLUE_800, weight=ft.FontWeight.W_500)
    )
    
    custom_capex_input = ft.TextField(
        label="CAPEX (M€)",
        value="9.5",
        width=140,
        visible=False,
        bgcolor=ft.Colors.BLUE_GREY_50,
        border_color=ft.Colors.BLUE_400,
        focused_border_color=ft.Colors.BLUE_600,
        label_style=ft.TextStyle(color=ft.Colors.BLUE_800, weight=ft.FontWeight.W_500)
    )
    
    custom_opex_input = ft.TextField(
        label="OPEX (k€/an)",
        value="285",
        width=140,
        visible=False,
        bgcolor=ft.Colors.BLUE_GREY_50,
        border_color=ft.Colors.BLUE_400,
        focused_border_color=ft.Colors.BLUE_600,
        label_style=ft.TextStyle(color=ft.Colors.BLUE_800, weight=ft.FontWeight.W_500)
    )
    
    custom_degradation_input = ft.TextField(
        label="Dégradation (%/an)",
        value="0.4",
        width=140,
        visible=False,
        bgcolor=ft.Colors.BLUE_GREY_50,
        border_color=ft.Colors.BLUE_400,
        focused_border_color=ft.Colors.BLUE_600,
        label_style=ft.TextStyle(color=ft.Colors.BLUE_800, weight=ft.FontWeight.W_500)
    )
    
    # Financial parameters with green styling
    custom_ppa_price_input = ft.TextField(
        label="Prix PPA (€/kWh)",
        value="0.075",
        width=140,
        visible=False,
        bgcolor=ft.Colors.GREEN_50,
        border_color=ft.Colors.GREEN_400,
        focused_border_color=ft.Colors.GREEN_600,
        label_style=ft.TextStyle(color=ft.Colors.GREEN_800, weight=ft.FontWeight.W_500)
    )
    
    custom_price_escalation_input = ft.TextField(
        label="Escalation prix (%)",
        value="2.0",
        width=140,
        visible=False,
        bgcolor=ft.Colors.GREEN_50,
        border_color=ft.Colors.GREEN_400,
        focused_border_color=ft.Colors.GREEN_600,
        label_style=ft.TextStyle(color=ft.Colors.GREEN_800, weight=ft.FontWeight.W_500)
    )
    
    custom_debt_ratio_input = ft.TextField(
        label="Ratio dette (%)",
        value="60",
        width=140,
        visible=False,
        bgcolor=ft.Colors.GREEN_50,
        border_color=ft.Colors.GREEN_400,
        focused_border_color=ft.Colors.GREEN_600,
        label_style=ft.TextStyle(color=ft.Colors.GREEN_800, weight=ft.FontWeight.W_500)
    )
    
    custom_interest_rate_input = ft.TextField(
        label="Taux intérêt (%)",
        value="2.5",
        width=140,
        visible=False,
        bgcolor=ft.Colors.GREEN_50,
        border_color=ft.Colors.GREEN_400,
        focused_border_color=ft.Colors.GREEN_600,
        label_style=ft.TextStyle(color=ft.Colors.GREEN_800, weight=ft.FontWeight.W_500)
    )
    
    # Grant parameters with orange styling
    custom_grant_masen_input = ft.TextField(
        label="Subvention MASEN (M€)",
        value="0.95",
        width=140,
        visible=False,
        bgcolor=ft.Colors.ORANGE_50,
        border_color=ft.Colors.ORANGE_400,
        focused_border_color=ft.Colors.ORANGE_600,
        label_style=ft.TextStyle(color=ft.Colors.ORANGE_800, weight=ft.FontWeight.W_500)
    )
    
    custom_grant_iresen_input = ft.TextField(
        label="Subvention IRESEN (M€)",
        value="0.475",
        width=140,
        visible=False,
        bgcolor=ft.Colors.ORANGE_50,
        border_color=ft.Colors.ORANGE_400,
        focused_border_color=ft.Colors.ORANGE_600,
        label_style=ft.TextStyle(color=ft.Colors.ORANGE_800, weight=ft.FontWeight.W_500)
    )
    
    custom_grant_connection_input = ft.TextField(
        label="Subvention connexion (M€)",
        value="0.285",
        width=140,
        visible=False,
        bgcolor=ft.Colors.ORANGE_50,
        border_color=ft.Colors.ORANGE_400,
        focused_border_color=ft.Colors.ORANGE_600,
        label_style=ft.TextStyle(color=ft.Colors.ORANGE_800, weight=ft.FontWeight.W_500)
    )
    
    custom_inputs_container = ft.Container(
        content=ft.Column([
            ft.Container(
                content=ft.Text("⚙️ Paramètres personnalisés", weight=ft.FontWeight.BOLD, size=16, color=ft.Colors.INDIGO_700),
                padding=ft.padding.only(bottom=15)
            ),
            
            # Technical parameters section
            ft.Container(
                content=ft.Column([
                    ft.Row([
                        ft.Icon(ft.Icons.SETTINGS, color=ft.Colors.BLUE_600, size=18),
                        ft.Text("Paramètres techniques", weight=ft.FontWeight.W_600, size=14, color=ft.Colors.BLUE_700)
                    ], spacing=8),
                    ft.Container(
                        content=ft.Row([
                            ft.Container(content=custom_production_input, expand=1),
                            ft.Container(content=custom_capex_input, expand=1),
                            ft.Container(content=custom_opex_input, expand=1),
                            ft.Container(content=custom_degradation_input, expand=1)
                        ], spacing=15),
                        padding=ft.padding.only(top=8)
                    )
                ], spacing=10),
                bgcolor=ft.Colors.BLUE_GREY_50,
                border_radius=10,
                padding=16,
                border=ft.border.all(1.5, ft.Colors.BLUE_200)
            ),
            
            # Financial parameters section
            ft.Container(
                content=ft.Column([
                    ft.Row([
                        ft.Icon(ft.Icons.ATTACH_MONEY, color=ft.Colors.GREEN_600, size=18),
                        ft.Text("Paramètres financiers", weight=ft.FontWeight.W_600, size=14, color=ft.Colors.GREEN_700)
                    ], spacing=8),
                    ft.Container(
                        content=ft.Row([
                            ft.Container(content=custom_ppa_price_input, expand=1),
                            ft.Container(content=custom_price_escalation_input, expand=1),
                            ft.Container(content=custom_debt_ratio_input, expand=1),
                            ft.Container(content=custom_interest_rate_input, expand=1)
                        ], spacing=15),
                        padding=ft.padding.only(top=8)
                    )
                ], spacing=10),
                bgcolor=ft.Colors.GREEN_50,
                border_radius=10,
                padding=16,
                border=ft.border.all(1.5, ft.Colors.GREEN_200)
            ),
            
            # Grants section
            ft.Container(
                content=ft.Column([
                    ft.Row([
                        ft.Icon(ft.Icons.CARD_GIFTCARD, color=ft.Colors.ORANGE_600, size=18),
                        ft.Text("Subventions", weight=ft.FontWeight.W_600, size=14, color=ft.Colors.ORANGE_700)
                    ], spacing=8),
                    ft.Container(
                        content=ft.Row([
                            ft.Container(content=custom_grant_masen_input, expand=1),
                            ft.Container(content=custom_grant_iresen_input, expand=1),
                            ft.Container(content=custom_grant_connection_input, expand=1),
                            ft.Container(width=140, expand=1)  # Empty space for alignment
                        ], spacing=15),
                        padding=ft.padding.only(top=8)
                    )
                ], spacing=10),
                bgcolor=ft.Colors.ORANGE_50,
                border_radius=10,
                padding=16,
                border=ft.border.all(1.5, ft.Colors.ORANGE_200)
            )
        ], spacing=16, scroll=ft.ScrollMode.AUTO),
        visible=False,
        padding=20,
        bgcolor=ft.Colors.WHITE,
        border_radius=12,
        border=ft.border.all(2, ft.Colors.INDIGO_100)
    )
    
    def on_location_change(e):
        is_custom = second_location_dropdown.value == "Custom"
        
        # Container visibility
        custom_inputs_container.visible = is_custom
        
        # All input fields visibility
        custom_production_input.visible = is_custom
        custom_capex_input.visible = is_custom
        custom_opex_input.visible = is_custom
        custom_ppa_price_input.visible = is_custom
        custom_degradation_input.visible = is_custom
        custom_price_escalation_input.visible = is_custom
        custom_grant_masen_input.visible = is_custom
        custom_grant_iresen_input.visible = is_custom
        custom_grant_connection_input.visible = is_custom
        custom_debt_ratio_input.visible = is_custom
        custom_interest_rate_input.visible = is_custom
        
        page_ref.update()
    
    second_location_dropdown.on_change = on_location_change
    
    def run_comparison(e):
        if second_location_dropdown.value:
            cf_df, _ = get_cashflow_and_sensitivity()
            
            # Prepare custom data if "Custom" is selected
            custom_data = None
            if second_location_dropdown.value == "Custom":
                try:
                    custom_data = {
                        'production_mwh_year1': float(custom_production_input.value),
                        'capex_meur': float(custom_capex_input.value),
                        'opex_keuros_year1': float(custom_opex_input.value),
                        'ppa_price_eur_kwh': float(custom_ppa_price_input.value),
                        'degradation': float(custom_degradation_input.value) / 100,  # Convert percentage to decimal
                        'price_escalation': float(custom_price_escalation_input.value) / 100,  # Convert percentage to decimal
                        'grant_meur_masen': float(custom_grant_masen_input.value),
                        'grant_meur_iresen': float(custom_grant_iresen_input.value),
                        'grant_meur_connection': float(custom_grant_connection_input.value),
                        'debt_ratio': float(custom_debt_ratio_input.value) / 100,  # Convert percentage to decimal
                        'interest_rate': float(custom_interest_rate_input.value) / 100  # Convert percentage to decimal
                    }
                except ValueError:
                    show_error("Veuillez entrer des valeurs numériques valides")
                    return
            
            show_chart(generate_scenario_comparison_fig, "Comparaison Scénarios", cf_df, latest_kpis, current_assumptions, second_location_dropdown.value, custom_data)
            page_ref.close(comparison_dialog)
        else:
            show_error("Veuillez sélectionner une 2ème localisation")
    
    def close_dialog(e):
        page_ref.close(comparison_dialog)
    
    comparison_dialog = ft.AlertDialog(
        modal=True,
        title=ft.Row([
            ft.Icon(ft.Icons.COMPARE_ARROWS, color=ft.Colors.INDIGO_600, size=24),
            ft.Text("Comparaison de Localisations", weight=ft.FontWeight.BOLD, size=18)
        ], spacing=10),
        content=ft.Container(
            content=ft.Column([
                # Header section
                ft.Container(
                    content=ft.Column([
                        ft.Text(f"📍 Localisation actuelle: {current_assumptions.location_name if current_assumptions else 'Non définie'}", 
                               weight=ft.FontWeight.W_600, size=14, color=ft.Colors.INDIGO_800),
                        ft.Divider(color=ft.Colors.INDIGO_200, thickness=1)
                    ], spacing=8),
                    padding=ft.padding.only(bottom=10)
                ),
                
                # Location selection
                ft.Container(
                    content=second_location_dropdown,
                    padding=ft.padding.only(bottom=10)
                ),
                
                # Scrollable custom inputs container
                ft.Container(
                    content=custom_inputs_container,
                    height=300,  # Fixed height for scrolling
                ),
                
                # Footer info
                ft.Container(
                    content=ft.Text(
                        "💡 Cette comparaison analysera les deux localisations avec les mêmes paramètres financiers de base.", 
                        size=11, color=ft.Colors.GREY_600, italic=True
                    ),
                    padding=ft.padding.only(top=10, left=8, right=8, bottom=8),
                    bgcolor=ft.Colors.GREY_50,
                    border_radius=6
                )
            ], spacing=0, scroll=ft.ScrollMode.AUTO),
            width=650,  # Increased width for better horizontal spacing
            height=500  # Increased height
        ),
        actions=[
            ft.TextButton(
                "Annuler", 
                on_click=close_dialog,
                style=ft.ButtonStyle(
                    color=ft.Colors.GREY_600,
                    overlay_color=ft.Colors.GREY_100
                )
            ),
            ft.ElevatedButton(
                "Comparer", 
                on_click=run_comparison, 
                bgcolor=ft.Colors.INDIGO_600, 
                color=ft.Colors.WHITE,
                style=ft.ButtonStyle(
                    elevation=2,
                    shape=ft.RoundedRectangleBorder(radius=8)
                ),
                icon=ft.Icons.ANALYTICS
            )
        ],
        actions_alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
        actions_padding=ft.padding.symmetric(horizontal=20, vertical=15)
    )
    
    page_ref.open(comparison_dialog)
def show_gantt_chart(e): 
    cf_df, _ = get_cashflow_and_sensitivity()
    show_chart(generate_gantt_chart_fig, "Gantt Chart", cf_df, latest_kpis, current_assumptions)
def show_lcoe_waterfall_chart(e): 
    cf_df, _ = get_cashflow_and_sensitivity()
    show_chart(generate_lcoe_waterfall_fig, "LCOE Waterfall", cf_df, latest_kpis, current_assumptions)

def profile_change_handler(e):
    selected_profile_name = e.control.value
    profile_data = DEFAULT_PROFILES.get(selected_profile_name, DEFAULT_PROFILES["Custom/Manual"])
    
    # Show/hide custom location input based on selection
    if custom_location_input:
        custom_location_input.visible = (selected_profile_name == "Custom/Manual")
    
    # Update location description
    if location_description_text:
        location_description_text.value = f"ℹ️ {profile_data.get('description', '')}"
        location_description_text.color = ft.Colors.BLUE_700
    
    fields_to_set = [
        (prod_input, profile_data.get("production_mwh_year1", 0.0)),
        (opex_input, profile_data.get("opex_keuros_year1", 0.0)),
        (capex_input, profile_data.get("capex_meur", 0.0)),
        (grant_masen_input, profile_data.get("grant_meur_masen", 0.0)),
        (grant_iresen_input, profile_data.get("grant_meur_iresen", 0.0)),
        (grant_connection_input, profile_data.get("grant_meur_connection", 0.0)),
        (grant_italy_input, profile_data.get("grant_meur_italy", 0.0))
    ]
    updated_controls = []
    for field_tf, val in fields_to_set:
        if field_tf:
            field_tf.value = str(val)
            field_tf.error_text = "" 
            updated_controls.append(field_tf)
    
    # Add location-related controls to update list
    if custom_location_input:
        updated_controls.append(custom_location_input)
    if location_description_text:
        updated_controls.append(location_description_text)
        
    if page_ref and updated_controls: page_ref.update(*updated_controls)

def save_picked_directory(e: ft.FilePickerResultEvent):
    global selected_output_path
    if e.path: selected_output_path = Path(e.path)
    else: selected_output_path = None
    if selected_folder_text_ref: 
        selected_folder_text_ref.value = f"Output folder: {selected_output_path}" if selected_output_path else "Output folder: Not selected"
    if page_ref and selected_folder_text_ref : page_ref.update(selected_folder_text_ref)

def export_results_to_excel(e=None):
    if not selected_output_path:
        kpi_display_ref.value = "Select output folder first."; kpi_display_ref.color = ft.Colors.RED
        if page_ref: page_ref.update(kpi_display_ref); return
    if latest_cash_flow_df is None:
        kpi_display_ref.value = "No model results. Run model first."; kpi_display_ref.color = ft.Colors.RED
        if page_ref: page_ref.update(kpi_display_ref); return
    try:
        dest_file = selected_output_path / "financial_model_output.xlsx"
        # Debug: Print current assumptions to verify they're correct
        print(f"DEBUG - Exporting with assumptions: years={current_assumptions.years}, location={current_assumptions.location_name}")
        export_excel_logic(current_assumptions, latest_cash_flow_df, latest_kpis, latest_sensitivity_df, dest_file)
        kpi_display_ref.value = f"Excel exported to: {dest_file} (Years: {current_assumptions.years})"; kpi_display_ref.color = ft.Colors.GREEN
    except Exception as ex:
        kpi_display_ref.value = f"Error exporting Excel: {ex}"; kpi_display_ref.color = ft.Colors.RED
    if page_ref: page_ref.update(kpi_display_ref)

def save_current_chart_to_file(e=None):
    if not selected_output_path:
        kpi_display_ref.value = "Select output folder first."; kpi_display_ref.color = ft.Colors.RED
        if page_ref: page_ref.update(kpi_display_ref); return
    if not current_fig_generator:
        kpi_display_ref.value = "No chart to save."; kpi_display_ref.color = ft.Colors.RED
        if page_ref: page_ref.update(kpi_display_ref); return
    try:
        safe_title = "".join(c for c in current_fig_title if c.isalnum() or c in " _-").strip().replace(" ", "_").lower() or "chart"
        dest_file = selected_output_path / f"{safe_title}.png"
        fig = current_fig_generator(*current_fig_args, output_path=dest_file) # Generators save if path is given
        if fig is not None: # Fallback if generator didn't save and returned fig
             plt.figure(fig.number); plt.savefig(dest_file, bbox_inches='tight'); plt.close(fig)
        kpi_display_ref.value = f"Chart '{current_fig_title}' saved to: {dest_file}"; kpi_display_ref.color = ft.Colors.GREEN
    except Exception as ex:
        kpi_display_ref.value = f"Error saving chart: {ex}"; kpi_display_ref.color = ft.Colors.RED
    if page_ref: page_ref.update(kpi_display_ref)

def save_all_charts_to_file(e=None):
    if not selected_output_path:
        kpi_display_ref.value = "Select output folder first."; kpi_display_ref.color = ft.Colors.RED
        if page_ref: page_ref.update(kpi_display_ref); return
    if latest_cash_flow_df is None:
        kpi_display_ref.value = "No model results. Run model first."; kpi_display_ref.color = ft.Colors.RED
        if page_ref: page_ref.update(kpi_display_ref); return
    
    try:
        # Define all charts to generate
        charts_to_generate = [
            (generate_fcfe_cumule_fig, "FCFE_Cumule", [latest_cash_flow_df]),
            (generate_dscr_fig, "DSCR", [latest_cash_flow_df]),
            (generate_sens_tri_fig, "Sensibilite_TRI", [latest_sensitivity_df]),
            (generate_structure_financement_pie_fig, "Structure_Financement", [latest_cash_flow_df]),
            (generate_scenario_comparison_fig, "Comparaison_Scenarios", [latest_cash_flow_df, latest_kpis, current_assumptions, "Dakhla", None]),
            (generate_gantt_chart_fig, "Gantt_Chart", [latest_cash_flow_df, latest_kpis, current_assumptions]),
            (generate_lcoe_waterfall_fig, "LCOE_Waterfall", [latest_cash_flow_df, latest_kpis, current_assumptions])
        ]
        
        saved_charts = []
        failed_charts = []
        
        for fig_generator, chart_name, args in charts_to_generate:
            try:
                dest_file = selected_output_path / f"{chart_name}.png"
                fig = fig_generator(*args, output_path=dest_file)
                if fig is not None:  # Fallback if generator didn't save and returned fig
                    plt.figure(fig.number)
                    plt.savefig(dest_file, bbox_inches='tight')
                    plt.close(fig)
                saved_charts.append(chart_name)
            except Exception as chart_ex:
                failed_charts.append(f"{chart_name}: {str(chart_ex)}")
        
        # Update status message
        if saved_charts and not failed_charts:
            kpi_display_ref.value = f"All {len(saved_charts)} charts saved successfully to: {selected_output_path}"
            kpi_display_ref.color = ft.Colors.GREEN
        elif saved_charts and failed_charts:
            kpi_display_ref.value = f"{len(saved_charts)} charts saved, {len(failed_charts)} failed. Check console for details."
            kpi_display_ref.color = ft.Colors.ORANGE
            print(f"Failed charts: {failed_charts}")
        else:
            kpi_display_ref.value = f"Failed to save charts: {failed_charts}"
            kpi_display_ref.color = ft.Colors.RED
            
    except Exception as ex:
        kpi_display_ref.value = f"Error saving all charts: {ex}"
        kpi_display_ref.color = ft.Colors.RED
    
    if page_ref: page_ref.update(kpi_display_ref)

# Authentication variables
is_authenticated = False
password_input = None
login_error_text = None

def authenticate_user(password_value):
    """Simple password authentication - in production, use proper hashing"""
    # You can change this password as needed
    correct_password = "solar2025"
    return password_value == correct_password

def show_login_screen():
    """Display the login/copyright screen"""
    global password_input, login_error_text
    
    def handle_login(e):
        global is_authenticated
        if authenticate_user(password_input.value):
            is_authenticated = True
            show_main_app()
        else:
            login_error_text.value = "❌ Incorrect password. Please try again."
            login_error_text.color = ft.Colors.RED
            page_ref.update()
    
    def handle_key_press(e):
        if e.key == "Enter":
            handle_login(e)
    
    password_input = ft.TextField(
        label="🔐 Enter Password",
        password=True,
        can_reveal_password=True,
        width=300,
        border_radius=10,
        filled=True,
        bgcolor=ft.Colors.WHITE,
        border_color=ft.Colors.BLUE_300,
        focused_border_color=ft.Colors.BLUE_600,
        on_submit=handle_login,
        on_change=lambda e: setattr(login_error_text, 'value', '') or page_ref.update()
    )
    
    login_error_text = ft.Text("", size=12, color=ft.Colors.RED)
    
    # Copyright and licensing information
    copyright_info = ft.Container(
        content=ft.Column([
            ft.Text("📄 Copyright & License Information", size=18, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_800),
            ft.Container(height=10),
            ft.Text("© 2024 Abdelhalim Serhani - All Rights Reserved", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.GREY_800),
            ft.Text("Business & Financial Consultant at @Agevolami", size=12, color=ft.Colors.GREY_600),
            ft.Container(height=15),
            ft.Column([
                ft.Text("📋 License Terms:", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_700),
                ft.Text("• This software is proprietary and confidential", size=11, color=ft.Colors.GREY_700),
                ft.Text("• Unauthorized copying, distribution, or modification is prohibited", size=11, color=ft.Colors.GREY_700),
                ft.Text("• For licensing inquiries, contact: <EMAIL>", size=11, color=ft.Colors.GREY_700),
                ft.Text("• This application is designed for solar energy financial modeling", size=11, color=ft.Colors.GREY_700),
            ], spacing=5),
            ft.Container(height=15),
            ft.Column([
                ft.Text("⚠️ Important Notice:", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.ORANGE_700),
                ft.Text("• Results are for informational purposes only", size=11, color=ft.Colors.GREY_700),
                ft.Text("• Professional financial advice should be sought for investment decisions", size=11, color=ft.Colors.GREY_700),
                ft.Text("• The author assumes no liability for financial decisions based on this tool", size=11, color=ft.Colors.GREY_700),
            ], spacing=5)
        ], horizontal_alignment=ft.CrossAxisAlignment.START, spacing=8),
        padding=25,
        bgcolor=ft.Colors.BLUE_50,
        border_radius=12,
        border=ft.border.all(2, ft.Colors.BLUE_200),
        width=600
    )
    
    login_container = ft.Container(
        content=ft.Column([
            ft.Container(
                content=ft.Column([
                    ft.Text("🌞 Solar Financial Modeling Suite", size=28, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_800),
                    ft.Text("Advanced Financial Analysis for Solar Energy Projects", size=14, color=ft.Colors.GREY_600, italic=True),
                    ft.Container(height=20),
                    copyright_info,
                    ft.Container(height=30),
                    ft.Text("🔐 Authentication Required", size=20, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_700),
                    ft.Text("Please enter the password to access the application", size=12, color=ft.Colors.GREY_600),
                    ft.Container(height=15),
                    password_input,
                    login_error_text,
                    ft.Container(height=15),
                    ft.ElevatedButton(
                        "🚀 Access Application",
                        on_click=handle_login,
                        style=ft.ButtonStyle(
                            bgcolor=ft.Colors.BLUE_600,
                            color=ft.Colors.WHITE,
                            elevation=5,
                            shape=ft.RoundedRectangleBorder(radius=10),
                            padding=ft.padding.symmetric(horizontal=30, vertical=15)
                        ),
                        width=200
                    ),
                    ft.Container(height=20),
                    ft.Text("💡 Hint: Default password is 'solar2025'", size=10, color=ft.Colors.GREY_500, italic=True)
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=5),
                padding=40,
                bgcolor=ft.Colors.WHITE,
                border_radius=15,
                border=ft.border.all(2, ft.Colors.BLUE_100),
                shadow=ft.BoxShadow(
                    spread_radius=2,
                    blur_radius=15,
                    color=ft.Colors.BLUE_100,
                    offset=ft.Offset(0, 5)
                )
            )
        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=0),
        expand=True,
        alignment=ft.alignment.center,
        bgcolor=ft.Colors.BLUE_50
    )
    
    page_ref.clean()
    page_ref.add(login_container)
    page_ref.update()
    password_input.focus()

def show_main_app():
    """Display the main application after successful authentication"""
    # Clear the page and show main app
    page_ref.clean()
    initialize_main_app()

def initialize_main_app():
    """Initialize the main application UI"""
    continue_initialize_main_app()

def main(p: ft.Page):
    global page_ref, selected_folder_text_ref, kpi_display_ref, chart_display
    global location_profile_dropdown, custom_location_input, location_description_text
    global ppa_price_input, prod_input, opex_input, capex_input
    global grant_masen_input, grant_iresen_input, grant_connection_input, grant_italy_input
    global years_input, degradation_input, price_escalation_input, opex_escalation_input
    global debt_ratio_input, interest_rate_input, debt_years_input, grace_years_input
    global tax_holiday_input, tax_rate_input, discount_rate_input, all_input_text_fields_list

    page_ref = p
    page_ref.title = "🌞 Solar Financial Modeling Suite"
    page_ref.theme_mode = ft.ThemeMode.LIGHT
    page_ref.theme = ft.Theme(
        color_scheme_seed=ft.Colors.BLUE,
        visual_density=ft.VisualDensity.COMFORTABLE
    )
    page_ref.vertical_alignment = ft.MainAxisAlignment.START
    page_ref.horizontal_alignment = ft.CrossAxisAlignment.START
    page_ref.scroll = ft.ScrollMode.AUTO
    page_ref.auto_scroll = False
    page_ref.padding = 20
    page_ref.bgcolor = ft.Colors.GREY_50
    
    # Show login screen first
    show_login_screen()

def continue_initialize_main_app():
    """Continue with the rest of the main app initialization"""
    global chart_display, kpi_display_ref, selected_folder_text_ref
    global location_profile_dropdown, custom_location_input, location_description_text
    global ppa_price_input, prod_input, opex_input, capex_input
    global grant_masen_input, grant_iresen_input, grant_connection_input, grant_italy_input
    global years_input, degradation_input, price_escalation_input, opex_escalation_input
    global debt_ratio_input, interest_rate_input, debt_years_input, grace_years_input
    global tax_holiday_input, tax_rate_input, discount_rate_input, all_input_text_fields_list

    chart_display = MatplotlibChart(expand=True)
    kpi_display_local = ft.Container(
        content=ft.Text("📊 KPIs will be shown here...", size=16, selectable=True, color=ft.Colors.GREY_700),
        padding=20,
        bgcolor=ft.Colors.WHITE,
        border_radius=10,
        border=ft.border.all(1, ft.Colors.BLUE_200),
        shadow=ft.BoxShadow(
            spread_radius=1,
            blur_radius=5,
            color=ft.Colors.BLUE_100,
            offset=ft.Offset(0, 2)
        )
    )
    kpi_display_ref = kpi_display_local.content

    # --- Define UI Controls ---
    tf_width_short = 140
    tf_width_medium = 170
    tf_width_long = 200
    
    # Enhanced styling for input fields
    def create_styled_textfield(label, value, width, **kwargs):
        return ft.TextField(
            label=label,
            value=str(value),
            width=width,
            border_radius=8,
            filled=True,
            bgcolor=ft.Colors.WHITE,
            border_color=ft.Colors.BLUE_200,
            focused_border_color=ft.Colors.BLUE_400,
            **kwargs
        )
    
    location_profile_dropdown = ft.Dropdown(
        label="📍 Location Profile", 
        options=[ft.dropdown.Option(name) for name in DEFAULT_PROFILES.keys()], 
        value=current_assumptions.location_name, 
        on_change=profile_change_handler, 
        width=220,
        border_radius=8,
        filled=True,
        bgcolor=ft.Colors.WHITE,
        border_color=ft.Colors.BLUE_200,
        focused_border_color=ft.Colors.BLUE_400
    )
    
    # Custom location input (only visible when Custom/Manual is selected)
    custom_location_input = create_styled_textfield(
        "🏗️ Custom Location Name", 
        "", 
        tf_width_long,
        hint_text="Enter your investment location in Morocco",
        visible=False
    )
    
    # Location description text
    location_description_text = ft.Text(
        f"ℹ️ {DEFAULT_PROFILES[current_assumptions.location_name].get('description', '')}",
        size=12,
        color=ft.Colors.BLUE_700,
        italic=True,
        width=600
    )
    
    ppa_price_input = create_styled_textfield("💰 PPA Price (€/kWh)", current_assumptions.ppa_price_eur_kwh, tf_width_long)
    prod_input = create_styled_textfield("⚡ Production (MWh y1)", current_assumptions.production_mwh_year1, tf_width_medium)
    opex_input = create_styled_textfield("🔧 OPEX (€k y1)", current_assumptions.opex_keuros_year1, tf_width_medium)
    capex_input = create_styled_textfield("🏗️ CAPEX (M€)", current_assumptions.capex_meur, tf_width_short)
    
    grant_masen_input = create_styled_textfield("🏛️ Grant MASEN (M€)", current_assumptions.grant_meur_masen, tf_width_long)
    grant_iresen_input = create_styled_textfield("🔬 Grant IRESEN (M€)", current_assumptions.grant_meur_iresen, tf_width_long)
    grant_connection_input = create_styled_textfield("🔌 Grant Connection (M€)", current_assumptions.grant_meur_connection, tf_width_long)
    grant_italy_input = create_styled_textfield("🇮🇹 Grant Italy (M€)", current_assumptions.grant_meur_italy, tf_width_long)

    years_input = create_styled_textfield("📅 Project Years", current_assumptions.years, tf_width_short, input_filter=ft.InputFilter(allow=True, regex_string=r"^[0-9]*$"))
    degradation_input = create_styled_textfield("📉 Degradation (%/yr)", current_assumptions.degradation * 100, tf_width_medium, hint_text="e.g. 0.4", suffix_text="%")
    price_escalation_input = create_styled_textfield("📈 PPA Escalation (%/yr)", current_assumptions.price_escalation * 100, tf_width_long, hint_text="e.g. 2.0", suffix_text="%")
    opex_escalation_input = create_styled_textfield("🔧📈 OPEX Escalation (%/yr)", current_assumptions.opex_escalation * 100, tf_width_long, hint_text="e.g. 2.0", suffix_text="%")

    debt_ratio_input = create_styled_textfield("💳 Debt Ratio (%)", current_assumptions.debt_ratio * 100, tf_width_medium, hint_text="e.g. 60.0", suffix_text="%")
    interest_rate_input = create_styled_textfield("📊 Interest Rate (%/yr)", current_assumptions.interest_rate * 100, tf_width_long, hint_text="e.g. 2.5", suffix_text="%")
    debt_years_input = create_styled_textfield("⏰ Debt Years", current_assumptions.debt_years, tf_width_short, input_filter=ft.InputFilter(allow=True, regex_string=r"^[0-9]*$"))
    grace_years_input = create_styled_textfield("⏳ Grace Years", current_assumptions.grace_years, tf_width_short, input_filter=ft.InputFilter(allow=True, regex_string=r"^[0-9]*$"))

    tax_holiday_input = create_styled_textfield("🏖️ Tax Holiday (Years)", current_assumptions.tax_holiday, tf_width_medium, input_filter=ft.InputFilter(allow=True, regex_string=r"^[0-9]*$"))
    tax_rate_input = create_styled_textfield("💸 Tax Rate (%)", current_assumptions.tax_rate * 100, tf_width_medium, hint_text="e.g. 15.0", suffix_text="%")
    discount_rate_input = create_styled_textfield("📉 Discount Rate (%)", current_assumptions.discount_rate * 100, tf_width_long, hint_text="e.g. 8.0", suffix_text="%")
    
    all_input_text_fields_list.extend([
        ppa_price_input, prod_input, opex_input, capex_input, grant_masen_input, grant_iresen_input,
        grant_connection_input, grant_italy_input, years_input, degradation_input, price_escalation_input,
        opex_escalation_input, debt_ratio_input, interest_rate_input, debt_years_input, grace_years_input,
        tax_holiday_input, tax_rate_input, discount_rate_input
    ])

    # Enhanced button styling
    def create_primary_button(text, on_click, icon=None, width=None):
        return ft.ElevatedButton(
            text=text,
            on_click=on_click,
            icon=icon,
            height=45,
            width=width,
            style=ft.ButtonStyle(
                bgcolor=ft.Colors.BLUE_600,
                color=ft.Colors.WHITE,
                elevation=3,
                shape=ft.RoundedRectangleBorder(radius=8)
            )
        )
    
    def create_secondary_button(text, on_click, icon=None, width=None):
        return ft.ElevatedButton(
            text=text,
            on_click=on_click,
            icon=icon,
            height=40,
            width=width,
            style=ft.ButtonStyle(
                bgcolor=ft.Colors.GREY_100,
                color=ft.Colors.GREY_800,
                elevation=2,
                shape=ft.RoundedRectangleBorder(radius=8)
            )
        )
    
    run_button = create_primary_button("🚀 Run Model & Update KPIs", update_assumptions_and_run, width=250)
    output_directory_picker = ft.FilePicker(on_result=save_picked_directory)
    page_ref.overlay.append(output_directory_picker)
    select_folder_button = create_secondary_button("📁 Select Output Folder", lambda _: output_directory_picker.get_directory_path(dialog_title="Select Output Folder"), ft.Icons.FOLDER_OPEN)
    selected_folder_text_local = ft.Text("📂 Output folder: Not selected", color=ft.Colors.GREY_600, size=14)
    selected_folder_text_ref = selected_folder_text_local
    export_excel_button = create_secondary_button("📊 Export to Excel", export_results_to_excel, ft.Icons.TABLE_CHART)
    save_chart_button = create_secondary_button("💾 Save Current Chart", save_current_chart_to_file, ft.Icons.SAVE)

    # --- Layout ---
    def create_section_card(title, content, icon=""):
        return ft.Container(
            content=ft.Column([
                ft.Text(f"{icon} {title}", size=18, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_800),
                ft.Divider(height=1, color=ft.Colors.BLUE_200),
                content
            ], spacing=10),
            padding=20,
            bgcolor=ft.Colors.WHITE,
            border_radius=12,
            border=ft.border.all(1, ft.Colors.BLUE_100),
            shadow=ft.BoxShadow(
                spread_radius=1,
                blur_radius=8,
                color=ft.Colors.BLUE_50,
                offset=ft.Offset(0, 2)
            )
        )
    
    # Core financials section
    core_financials = create_section_card(
        "Location & Core Financials", 
        ft.Column([
            ft.Row([location_profile_dropdown, ppa_price_input], alignment=ft.MainAxisAlignment.START, spacing=15),
            location_description_text,
            custom_location_input,
            ft.Row([prod_input, opex_input, capex_input], alignment=ft.MainAxisAlignment.START, spacing=15)
        ], spacing=15),
        "🏢"
    )
    
    # Grants section
    grants_section = create_section_card(
        "Grant Funding (M€)",
        ft.Column([
            ft.Row([grant_masen_input, grant_iresen_input], alignment=ft.MainAxisAlignment.START, spacing=15),
            ft.Row([grant_connection_input, grant_italy_input], alignment=ft.MainAxisAlignment.START, spacing=15)
        ], spacing=15),
        "💰"
    )
    
    # General parameters section
    general_params = create_section_card(
        "General Parameters",
        ft.Column([
            ft.Row([years_input, degradation_input], alignment=ft.MainAxisAlignment.START, spacing=15),
            ft.Row([price_escalation_input, opex_escalation_input], alignment=ft.MainAxisAlignment.START, spacing=15)
        ], spacing=15),
        "⚙️"
    )
    
    # Debt parameters section
    debt_params = create_section_card(
        "Debt Parameters",
        ft.Column([
            ft.Row([debt_ratio_input, interest_rate_input], alignment=ft.MainAxisAlignment.START, spacing=15),
            ft.Row([debt_years_input, grace_years_input], alignment=ft.MainAxisAlignment.START, spacing=15)
        ], spacing=15),
        "🏦"
    )
    
    # Tax parameters section
    tax_params = create_section_card(
        "Tax & Discount Parameters",
        ft.Row([tax_holiday_input, tax_rate_input, discount_rate_input], alignment=ft.MainAxisAlignment.START, spacing=15),
        "📊"
    )
    
    # Tips section for Italian businesses investing in Morocco
    tips_section = create_section_card(
        "💡 Tips for Italian Businesses Investing in Moroccan Renewable Energy",
        ft.Column([
            ft.Text("🎯 Getting Started:", weight=ft.FontWeight.BOLD, color=ft.Colors.GREEN_700),
            ft.Text("• Select a Moroccan location from the dropdown (based on real renewable energy projects)", size=13),
            ft.Text("• For custom locations, choose 'Custom/Manual' and enter your specific investment site", size=13),
            ft.Text("• PPA Price: Typical range 0.04-0.08 €/kWh for solar projects in Morocco", size=13),
            ft.Divider(height=1, color=ft.Colors.GREY_300),
            ft.Text("💰 Financial Parameters:", weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_700),
            ft.Text("• CAPEX: Usually 8-10 M€ for 5MW solar projects", size=13),
            ft.Text("• OPEX: Typically 200-350 k€/year (2-4% of CAPEX annually)", size=13),
            ft.Text("• Italian Grant: Available for Italian companies investing in Morocco", size=13),
            ft.Text("• MASEN/IRESEN Grants: Moroccan government incentives for renewable energy", size=13),
            ft.Divider(height=1, color=ft.Colors.GREY_300),
            ft.Text("📊 Key Metrics:", weight=ft.FontWeight.BOLD, color=ft.Colors.ORANGE_700),
            ft.Text("• Target IRR: 8-12% for renewable energy projects", size=13),
            ft.Text("• DSCR: Should stay above 1.2 for bankable projects", size=13),
            ft.Text("• Charts are dynamically generated from your input data - not predefined!", size=13, weight=ft.FontWeight.BOLD, color=ft.Colors.RED_600)
        ], spacing=8),
        "💡"
    )
    
    inputs_layout = ft.Column([
        tips_section,
        core_financials,
        grants_section,
        general_params,
        debt_params,
        tax_params,
        ft.Container(
            content=ft.Row([run_button], alignment=ft.MainAxisAlignment.CENTER),
            padding=ft.padding.symmetric(vertical=20)
        )
    ], spacing=20, width=900, scroll=ft.ScrollMode.AUTO)

    # Output actions section
    output_actions_layout = create_section_card(
        "Export & Save Options",
        ft.Column([
            ft.Row([select_folder_button, export_excel_button], alignment=ft.MainAxisAlignment.START, spacing=15),
            selected_folder_text_local,
            ft.Row([save_chart_button], alignment=ft.MainAxisAlignment.START)
        ], spacing=15),
        "💾"
    )
    
    top_controls_column = ft.Column([inputs_layout, output_actions_layout], spacing=20, scroll=ft.ScrollMode.AUTO)

    # Enhanced chart buttons
    def create_chart_button(text, on_click, icon="📊"):
        return ft.ElevatedButton(
            text=f"{icon} {text}",
            on_click=on_click,
            width=280,
            height=45,
            style=ft.ButtonStyle(
                bgcolor=ft.Colors.BLUE_50,
                color=ft.Colors.BLUE_800,
                elevation=2,
                shape=ft.RoundedRectangleBorder(radius=8),
                side=ft.BorderSide(1, ft.Colors.BLUE_200)
            )
        )

    chart_buttons_list = [
        create_chart_button("FCFE Cumulé", show_fcfe_chart, "💰"),
        create_chart_button("DSCR", show_dscr_chart, "📈"),
        create_chart_button("Sensibilité TRI", show_sens_tri_chart, "🎯"),
        create_chart_button("Structure Financement", show_structure_financement_chart, "🥧"),
        create_chart_button("Comparaison Scénarios", show_scenario_comparison_chart, "⚖️"),
        create_chart_button("Gantt Chart", show_gantt_chart, "📅"),
        create_chart_button("LCOE Waterfall", show_lcoe_waterfall_chart, "🌊"),
        ft.Divider(height=20, color=ft.Colors.BLUE_200),
        ft.ElevatedButton(
            text="💾 Save All Charts",
            on_click=save_all_charts_to_file,
            width=280,
            height=50,
            style=ft.ButtonStyle(
                bgcolor=ft.Colors.GREEN_50,
                color=ft.Colors.GREEN_800,
                elevation=3,
                shape=ft.RoundedRectangleBorder(radius=8),
                side=ft.BorderSide(2, ft.Colors.GREEN_300)
            )
        ),
    ]
    
    chart_buttons_column = ft.Column([
        ft.Text("📊 Chart Selection", size=18, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_800)
    ] + chart_buttons_list, spacing=12)

    sidebar_container = ft.Container(
        content=chart_buttons_column, 
        width=320, 
        padding=20,
        bgcolor=ft.Colors.WHITE,
        border_radius=12,
        border=ft.border.all(1, ft.Colors.BLUE_100),
        shadow=ft.BoxShadow(
            spread_radius=1,
            blur_radius=8,
            color=ft.Colors.BLUE_50,
            offset=ft.Offset(0, 2)
        )
    )

    # Enhanced chart display area
    chart_container = ft.Container(
        content=chart_display,
        bgcolor=ft.Colors.WHITE,
        border_radius=12,
        border=ft.border.all(1, ft.Colors.BLUE_100),
        padding=10,
        shadow=ft.BoxShadow(
            spread_radius=1,
            blur_radius=8,
            color=ft.Colors.BLUE_50,
            offset=ft.Offset(0, 2)
        )
    )
    
    main_content_area = ft.Column([
        kpi_display_local, 
        chart_container
    ], expand=True, alignment=ft.MainAxisAlignment.START, horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=20)

    # Header section
    header = ft.Container(
        content=ft.Column([
            ft.Text("🌞 Solar Financial Modeling Suite", size=32, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_800),
            ft.Text("Advanced financial analysis for solar energy projects", size=16, color=ft.Colors.GREY_600, italic=True)
        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=5),
        padding=ft.padding.symmetric(vertical=20),
        bgcolor=ft.Colors.WHITE,
        border_radius=12,
        border=ft.border.all(1, ft.Colors.BLUE_100),
        shadow=ft.BoxShadow(
            spread_radius=1,
            blur_radius=8,
            color=ft.Colors.BLUE_50,
            offset=ft.Offset(0, 2)
        )
    )

    # Creator information footer
    creator_info = ft.Container(
        content=ft.Column([
            ft.Text("👨‍💼 Created by", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_800),
            ft.Text("Abdelhalim Serhani", size=16, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_900),
            ft.Text("Business & Financial Consultant at @Agevolami", size=12, color=ft.Colors.GREY_700),
            ft.Container(height=8),
            ft.Column([
                ft.Row([
                    ft.Icon(ft.Icons.EMAIL, size=14, color=ft.Colors.BLUE_600),
                    ft.Text("<EMAIL>", size=11, color=ft.Colors.BLUE_700)
                ], spacing=5),
                ft.Row([
                    ft.Icon(ft.Icons.EMAIL, size=14, color=ft.Colors.BLUE_600),
                    ft.Text("<EMAIL>", size=11, color=ft.Colors.BLUE_700)
                ], spacing=5),
                ft.Row([
                    ft.Icon(ft.Icons.CODE, size=14, color=ft.Colors.BLUE_600),
                    ft.Text("Github: serhabdel", size=11, color=ft.Colors.BLUE_700)
                ], spacing=5),
                ft.Row([
                    ft.Icon(ft.Icons.BUSINESS, size=14, color=ft.Colors.BLUE_600),
                    ft.Text("LinkedIn: Abdelhalim Serhani", size=11, color=ft.Colors.BLUE_700)
                ], spacing=5)
            ], spacing=4)
        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=8),
        padding=20,
        bgcolor=ft.Colors.RED_50,
        border_radius=12,
        border=ft.border.all(2, ft.Colors.RED_200),
        shadow=ft.BoxShadow(
            spread_radius=1,
            blur_radius=8,
            color=ft.Colors.RED_100,
            offset=ft.Offset(0, 2)
        )
    )

    # Main layout with responsive design
    main_row = ft.Row([
        ft.Column([
            sidebar_container,
            ft.Container(height=20),
            creator_info
        ], spacing=0),
        ft.Container(content=main_content_area, expand=True, padding=ft.padding.only(left=20))
    ], vertical_alignment=ft.CrossAxisAlignment.START, expand=True)

    # Wrap everything in a scrollable container
    main_content = ft.Column([
        header,
        ft.Container(height=20),  # Spacer
        top_controls_column,
        ft.Container(height=20),  # Spacer
        main_row
    ], scroll=ft.ScrollMode.AUTO, expand=True)
    
    page_ref.add(main_content)
    
    profile_change_handler(ft.ControlEvent(target=None, name=None, data=None, control=location_profile_dropdown, page=None)) # Initial population
    update_assumptions_and_run(None)

if __name__ == "__main__":
    ft.app(target=main)