#!/usr/bin/env python3
"""
Test the DOCX export fix
"""

def test_attribute_access():
    """Test that we can access the correct attributes"""
    print("🔬 Testing Attribute Access Fix")
    print("=" * 35)
    
    try:
        # Import the data model
        from core.enhanced_data_models import EnhancedProjectAssumptions
        
        # Create test assumptions
        assumptions = EnhancedProjectAssumptions()
        
        print("✅ EnhancedProjectAssumptions imported successfully")
        
        # Test the attributes used in DOCX export
        test_attributes = [
            ('location_name', assumptions.location_name),
            ('capacity_mw', assumptions.capacity_mw),
            ('capex_meur', assumptions.capex_meur),
            ('project_life_years', assumptions.project_life_years),  # This was the issue
            ('production_mwh_year1', assumptions.production_mwh_year1),
            ('ppa_price_eur_kwh', assumptions.ppa_price_eur_kwh),
            ('grant_meur_italy', assumptions.grant_meur_italy),
            ('grant_meur_simest', assumptions.grant_meur_simest),
            ('grant_meur_masen', assumptions.grant_meur_masen),
            ('grant_meur_connection', assumptions.grant_meur_connection),
            ('debt_ratio', assumptions.debt_ratio)
        ]
        
        print("\n📋 Testing DOCX export attributes:")
        for attr_name, attr_value in test_attributes:
            print(f"   ✅ {attr_name}: {attr_value}")
        
        # Test the specific line that was failing
        project_life_text = f'{assumptions.project_life_years} years'
        print(f"\n🎯 Fixed line test: 'Project Life: {project_life_text}'")
        
        return True
        
    except AttributeError as e:
        print(f"❌ Attribute error: {e}")
        return False
    except Exception as e:
        print(f"❌ Other error: {e}")
        return False

def test_docx_export_simulation():
    """Simulate the DOCX export process"""
    print("\n🔬 Simulating DOCX Export Process")
    print("=" * 40)
    
    try:
        from core.enhanced_data_models import EnhancedProjectAssumptions
        
        # Create test assumptions
        assumptions = EnhancedProjectAssumptions(
            location_name="Test Morocco Project",
            capacity_mw=10.0,
            capex_meur=8.5,
            project_life_years=25,
            production_mwh_year1=22000,
            ppa_price_eur_kwh=0.055,
            grant_meur_italy=1.2,
            grant_meur_simest=0.5,
            grant_meur_masen=0.8,
            grant_meur_connection=0.3
        )
        
        # Simulate the project data creation (the part that was failing)
        def calculate_total_grants():
            return (assumptions.grant_meur_italy + 
                   assumptions.grant_meur_simest + 
                   assumptions.grant_meur_masen + 
                   assumptions.grant_meur_connection)
        
        project_data = [
            ('Project Name', getattr(assumptions, 'project_name', assumptions.location_name)),
            ('Technology', 'Solar Photovoltaic'),
            ('Capacity', f'{assumptions.capacity_mw} MW'),
            ('Location', 'Morocco'),
            ('Total Investment', f'€{assumptions.capex_meur:.1f}M'),
            ('Grant Support', f'€{calculate_total_grants():.1f}M ({(calculate_total_grants()/assumptions.capex_meur*100):.1f}% of CAPEX)'),
            ('Project Life', f'{assumptions.project_life_years} years'),  # This was the failing line
            ('Annual Production (Year 1)', f'{assumptions.production_mwh_year1:,.0f} MWh'),
            ('PPA Price', f'{assumptions.ppa_price_eur_kwh:.3f} EUR/kWh')
        ]
        
        print("✅ Project data creation successful:")
        for param, value in project_data:
            print(f"   📊 {param}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ Simulation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔧 DOCX Export Fix Verification")
    print("=" * 50)
    
    # Test 1: Attribute access
    attr_test = test_attribute_access()
    
    # Test 2: DOCX export simulation
    export_test = test_docx_export_simulation()
    
    print("\n📋 TEST RESULTS:")
    print(f"   Attribute Access: {'✅ PASS' if attr_test else '❌ FAIL'}")
    print(f"   Export Simulation: {'✅ PASS' if export_test else '❌ FAIL'}")
    
    if attr_test and export_test:
        print("\n🎉 DOCX Export Fix is working!")
        print("✅ The main app should now export DOCX files successfully")
    else:
        print("\n🚨 DOCX Export Fix needs more work")
        print("❌ Check the error messages above")
