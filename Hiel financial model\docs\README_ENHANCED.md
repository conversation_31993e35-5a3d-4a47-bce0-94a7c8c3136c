# Enhanced Renewable Energy Financial Model

## Professional Edition for Italian-Moroccan Renewable Energy Projects

### Overview

This enhanced financial model is a comprehensive, professionally-validated tool designed specifically for consulting firms advising Italian clients on renewable energy investments in Morocco. The model incorporates extensive industry research, validated assumptions, and advanced risk analysis capabilities.

### Key Features

✅ **Industry-Validated Assumptions**
- Corrected grant structure (removed IRESEN for commercial projects)
- Validated Italian government support through Mattei Plan
- WACC benchmarks based on 2024 market data
- LCOE benchmarks aligned with global standards

✅ **Advanced Financial Modeling**
- Terminal value calculations (perpetuity growth & exit multiple)
- Working capital modeling
- Enhanced OPEX structure with insurance and land lease
- Realistic performance degradation curves

✅ **Comprehensive Risk Analysis**
- Monte Carlo simulation (1000+ runs)
- Multi-variable sensitivity analysis
- Scenario planning (base, conservative, optimistic, stress)
- Industry benchmark validation

✅ **Professional UI**
- Modern Flet-based desktop application
- Intuitive parameter input with validation
- Real-time results visualization
- Export capabilities for client presentations

### Installation

#### Prerequisites
- Python 3.9 or higher
- Windows 10/11 (optimized for desktop use)

#### Setup Instructions

1. **Clone or download the project**
   ```bash
   cd "d:/pro projects/flet/Hiel financial model"
   ```

2. **Create virtual environment**
   ```bash
   python -m venv venv_enhanced
   venv_enhanced\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements_enhanced.txt
   ```

4. **Run the enhanced application**
   ```bash
   python enhanced_main.py
   ```

### Quick Start Guide

#### 1. Project Setup
- Launch the application
- Navigate to "Project Setup" tab
- Configure basic parameters:
  - Project name and capacity
  - CAPEX and financial structure
  - Revenue parameters
  - Grant structure (validated assumptions)

#### 2. Run Financial Model
- Click "Run Model" to execute calculations
- Review results in "Financial Model" tab
- Key metrics displayed: IRR, NPV, LCOE, DSCR

#### 3. Validation & Benchmarks
- Check "Validation & Benchmarks" tab
- Review warnings and recommendations
- Compare against industry benchmarks

#### 4. Risk Analysis
- Explore "Sensitivity Analysis" for parameter impacts
- Run "Monte Carlo" simulation for risk assessment
- Review "Scenarios" for different market conditions

#### 5. Export Results
- Use "Export & Reports" tab
- Generate Excel reports for client presentations
- Include validation notes and recommendations

### Model Structure

```
Hiel financial model/
├── core/
│   ├── enhanced_financial_model.py    # Core calculation engine
│   ├── enhanced_data_models.py        # Data structures
│   ├── model_validation.py            # Validation framework
│   └── financial_model_logic.py       # Original model (legacy)
├── enhanced_main.py                   # Enhanced UI application
├── main.py                           # Original application
├── ENHANCED_MODEL_DOCUMENTATION.md   # Detailed documentation
├── README_ENHANCED.md                # This file
└── requirements_enhanced.txt         # Dependencies
```

### Key Improvements Over Original Model

#### 1. Grant Structure Corrections
**Before**: Included IRESEN grants for commercial projects
**After**: 
- IRESEN removed (research-only)
- Italian support validated via Mattei Plan
- MASEN grants for strategic projects
- Grid connection subsidies

#### 2. Enhanced Financial Calculations
**Before**: Basic DCF with simple assumptions
**After**:
- Terminal value with multiple methods
- Working capital modeling
- Enhanced OPEX structure
- Realistic degradation curves

#### 3. Risk Analysis
**Before**: Limited sensitivity analysis
**After**:
- Monte Carlo simulation
- Multi-variable sensitivity
- Comprehensive scenario planning
- Industry benchmark validation

#### 4. Professional Validation
**Before**: Basic parameter checks
**After**:
- Industry benchmark validation
- Comprehensive warning system
- Professional recommendations
- Detailed documentation

### Usage for Consulting Firms

#### Client Presentations
1. **Start with validation**: Show model meets industry standards
2. **Present base case**: Core financial metrics and assumptions
3. **Discuss risks**: Sensitivity analysis and Monte Carlo results
4. **Show scenarios**: Different market conditions and outcomes
5. **Provide recommendations**: Based on validation results

#### Due Diligence Support
- Use validation framework for assumption checking
- Export detailed reports for investor presentations
- Customize scenarios based on client risk appetite
- Provide benchmark comparisons for context

#### Ongoing Monitoring
- Regular model updates with market data
- Quarterly validation against actual performance
- Annual benchmark updates
- Client-specific customizations

### Model Validation

The model has been validated against:

#### Industry Benchmarks
- **LCOE**: Global average €0.044/kWh (2024)
- **WACC**: Morocco range 9-12%
- **IRR**: Target >12% for equity investors
- **CAPEX**: €600k-1.2M per MW

#### Regulatory Framework
- Morocco corporate tax: 31%
- Renewable energy incentives
- Grid connection requirements
- Environmental compliance

#### Financial Structure
- Debt ratio: 65-75% (industry standard)
- DSCR minimum: 1.20 (lender requirement)
- Interest rates: Current market conditions
- Grant assumptions: Official sources

### Troubleshooting

#### Common Issues

1. **Import Errors**
   - Ensure all dependencies installed: `pip install -r requirements_enhanced.txt`
   - Check Python version: 3.9+ required

2. **Model Validation Warnings**
   - Review assumptions against benchmarks
   - Check grant assumptions with official sources
   - Validate technical parameters

3. **Performance Issues**
   - Reduce Monte Carlo simulations for faster results
   - Use sensitivity analysis for quick parameter testing

4. **Export Problems**
   - Ensure write permissions in output directory
   - Check Excel file not open in another application

### Support and Maintenance

#### Regular Updates
- Monthly: Market benchmark updates
- Quarterly: Regulatory changes review
- Annually: Comprehensive model validation

#### Customization Services
- Client-specific parameter sets
- Custom scenario definitions
- Additional risk factors
- Integration with client systems

### Disclaimer

This model is provided for professional consulting use. Results depend on input assumptions and market conditions. Users should:

- Validate all assumptions with official sources
- Consider qualitative factors not captured in the model
- Regularly update parameters based on market changes
- Seek professional advice for investment decisions

### Contact Information

For technical support, customization requests, or model updates:
- Review documentation in `ENHANCED_MODEL_DOCUMENTATION.md`
- Check validation results for specific recommendations
- Ensure regular updates with current market data

### License

This enhanced model is designed for professional consulting use. Please ensure compliance with all applicable regulations and seek appropriate professional advice for investment decisions.