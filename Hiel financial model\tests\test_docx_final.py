#!/usr/bin/env python3
"""
Final test for DOCX export with correct attributes
"""

def test_docx_export_attributes():
    """Test the exact attributes used in DOCX export"""
    print("🔬 Testing DOCX Export Attributes (Final)")
    print("=" * 45)
    
    try:
        from core.enhanced_data_models import EnhancedProjectAssumptions
        
        # Create test assumptions
        assumptions = EnhancedProjectAssumptions()
        
        print("✅ EnhancedProjectAssumptions created")
        
        # Test all attributes used in DOCX export
        print("\n📋 Testing DOCX export attributes:")
        
        # Project data attributes
        project_name = getattr(assumptions, 'project_name', assumptions.location_name)
        print(f"   ✅ Project Name: {project_name}")
        
        capacity = f'{assumptions.capacity_mw} MW'
        print(f"   ✅ Capacity: {capacity}")
        
        capex = f'€{assumptions.capex_meur:.1f}M'
        print(f"   ✅ Total Investment: {capex}")
        
        # Fixed attribute: project_life_years (not years)
        project_life = f'{assumptions.project_life_years} years'
        print(f"   ✅ Project Life: {project_life}")
        
        production = f'{assumptions.production_mwh_year1:,.0f} MWh'
        print(f"   ✅ Annual Production: {production}")
        
        ppa_price = f'{assumptions.ppa_price_eur_kwh:.3f} EUR/kWh'
        print(f"   ✅ PPA Price: {ppa_price}")
        
        # Grant attributes
        italy_grant = f'{assumptions.grant_meur_italy:.2f}'
        print(f"   ✅ Italian Grant: €{italy_grant}M")
        
        # Fixed attribute: grant_meur_simest_africa (not grant_meur_simest)
        simest_grant = f'{getattr(assumptions, "grant_meur_simest_africa", 0.5):.2f}'
        print(f"   ✅ SIMEST Grant: €{simest_grant}M")
        
        masen_grant = f'{assumptions.grant_meur_masen:.2f}'
        print(f"   ✅ MASEN Grant: €{masen_grant}M")
        
        connection_grant = f'{assumptions.grant_meur_connection:.2f}'
        print(f"   ✅ Connection Grant: €{connection_grant}M")
        
        # Calculate total grants (like in the app)
        def calculate_total_grants():
            total = assumptions.grant_meur_italy
            total += assumptions.grant_meur_masen
            total += assumptions.grant_meur_connection
            total += getattr(assumptions, 'grant_meur_simest_africa', 0.5)
            return total
        
        total_grants = calculate_total_grants()
        grant_support = f'€{total_grants:.1f}M ({(total_grants/assumptions.capex_meur*100):.1f}% of CAPEX)'
        print(f"   ✅ Grant Support: {grant_support}")
        
        # Financing structure
        debt_amount = assumptions.capex_meur * assumptions.debt_ratio
        equity_amount = assumptions.capex_meur - debt_amount - total_grants
        
        print(f"   ✅ Debt Amount: €{debt_amount:.1f}M")
        print(f"   ✅ Equity Amount: €{equity_amount:.1f}M")
        
        print("\n🎉 All DOCX export attributes are accessible!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_signature_creation():
    """Test the signature creation process"""
    print("\n🎨 Testing Signature Creation")
    print("=" * 35)
    
    try:
        from docx import Document
        from docx.shared import Pt
        from docx.enum.text import WD_ALIGN_PARAGRAPH
        from docx.enum.table import WD_TABLE_ALIGNMENT
        
        # Create test document
        doc = Document()
        
        # Add signature header
        signature_header = doc.add_heading('Report Prepared By', level=2)
        signature_header.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # Create signature table
        sig_table = doc.add_table(rows=1, cols=1)
        sig_table.style = 'Table Grid'
        sig_table.alignment = WD_TABLE_ALIGNMENT.CENTER
        
        # Add signature content
        sig_cell = sig_table.rows[0].cells[0]
        sig_para = sig_cell.paragraphs[0]
        sig_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # Add name
        name_run = sig_para.add_run('Abdelhalim Serhani')
        name_run.font.size = Pt(16)
        name_run.font.bold = True
        
        sig_para.add_run('\n')
        
        # Add title
        title_run = sig_para.add_run('Business & Financial Consulting')
        title_run.font.size = Pt(12)
        title_run.font.italic = True
        
        sig_para.add_run('\n')
        
        # Add company
        company_run = sig_para.add_run('@ ')
        company_run.font.size = Pt(12)
        
        agevolami_run = sig_para.add_run('Agevolami.it')
        agevolami_run.font.size = Pt(12)
        agevolami_run.font.bold = True
        agevolami_run.font.underline = True
        
        sig_para.add_run('\n\n')
        
        # Add tagline
        tagline_run = sig_para.add_run('🌟 Empowering Renewable Energy Investments in Morocco & Beyond 🌟')
        tagline_run.font.size = Pt(10)
        tagline_run.font.italic = True
        
        # Save test
        filename = 'test_signature.docx'
        doc.save(filename)
        
        print("✅ Signature creation successful")
        print(f"✅ Test file saved: {filename}")
        return True
        
    except Exception as e:
        print(f"❌ Signature test failed: {e}")
        return False

if __name__ == "__main__":
    print("🔧 Final DOCX Export Test")
    print("=" * 50)
    
    # Test 1: Attribute access
    attr_test = test_docx_export_attributes()
    
    # Test 2: Signature creation
    sig_test = test_signature_creation()
    
    print("\n📋 FINAL TEST RESULTS:")
    print(f"   Attribute Access: {'✅ PASS' if attr_test else '❌ FAIL'}")
    print(f"   Signature Creation: {'✅ PASS' if sig_test else '❌ FAIL'}")
    
    if attr_test and sig_test:
        print("\n🎉 DOCX Export is ready!")
        print("✅ All fixes applied successfully")
        print("✅ Your professional signature is included")
        print("✅ The main app should now export DOCX files")
    else:
        print("\n🚨 DOCX Export still has issues")
        print("❌ Check the error messages above")
