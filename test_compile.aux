\relax 
\providecommand\babel@aux[2]{}
\@nameuse{bbl@beforestart}
\catcode `:\active 
\catcode `;\active 
\catcode `!\active 
\catcode `?\active 
\providecommand\hyper@newdestlabel[2]{}
\providecommand\HyField@AuxAddToFields[1]{}
\providecommand\HyField@AuxAddToCoFields[2]{}
\global\mtcsecondpartfalse
\@writefile{toc}{\@ifundefined {etoctocstyle}{\let \etoc@startlocaltoc \@gobble \let \etoc@settocdepth \@gobble \let \etoc@depthtag \@gobble \let \etoc@setlocaltop \@gobble }{}}
\babel@aux{french}{}
\@writefile{toc}{\contentsline {chapter}{\numberline {1}Test Chapter}{1}{chapter.1}\protected@file@percent }
\@writefile{lof}{\addvspace {10\p@ }}
\@writefile{lot}{\addvspace {10\p@ }}
\@writefile{lof}{\contentsline {xchapter}{Test Chapter}{1}{chapter.1}\protected@file@percent }
\@writefile{lot}{\contentsline {xchapter}{Test Chapter}{1}{chapter.1}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {1.1}{\ignorespaces Test Table}}{1}{table.1.1}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {1.1}{\ignorespaces Test Figure}}{1}{figure.1.1}\protected@file@percent }
\gdef \@abspage@last{1}
