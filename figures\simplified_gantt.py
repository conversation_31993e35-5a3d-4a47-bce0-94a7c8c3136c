"""simplified_gantt.py

Version simplifiée du diagramme de Gantt qui devrait fonctionner sur tous les systèmes.
Génère une visualisation de l'échéancier du projet solaire PV 10 MW au Maroc.
"""

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import matplotlib.patches as mpatches

# Configuration
plt.rcParams.update({
    'font.family': 'Calibri, sans-serif',
    'font.size': 10,
    'figure.figsize': (12, 8),
    'figure.dpi': 300,
    'axes.titlesize': 14,
    'axes.titleweight': 'bold',
    'axes.titlepad': 14,
    'axes.labelsize': 12,
    'xtick.labelsize': 10,
    'ytick.labelsize': 10,
    'legend.fontsize': 10,
    'figure.titlesize': 16,
})

# Palette de couleurs professionnelle
colors = {
    'phase1': '#1f77b4',  # Bleu - Études préliminaires
    'phase2': '#2ca02c',  # Vert - Autorisations
    'phase3': '#d62728',  # Rouge - Financement
    'phase4': '#ff7f0e',  # Orange - Construction
    'phase5': '#9467bd',  # Violet - Mise en service
    'background': '#f9f9f9',  # Gris très clair - Fond
    'text': '#333333',  # Gris foncé - Texte
    'grid': '#dddddd',  # Gris clair - Grille
    'milestone': '#e31a1c',  # Rouge vif - Jalons
    'milestone_line': '#555555',  # Gris foncé - Lignes de jalon
    'border': '#bbbbbb',  # Gris moyen - Bordures
}

# Créer la figure sans utiliser de dates (approche numérique)
fig, ax = plt.subplots(figsize=(12, 8))
fig.set_facecolor(colors['background'])
ax.set_facecolor(colors['background'])

# Ajout d'une bordure fine
for spine in ax.spines.values():
    spine.set_color(colors['border'])
    spine.set_linewidth(0.8)

# Définir les phases et leurs tâches
phases = {
    'phase1': {
        'name': 'Études préliminaires',
        'tasks': [
            {'name': 'Étude de préfaisabilité', 'start': 0, 'duration': 2},
            {'name': 'Étude de ressource solaire', 'start': 0.5, 'duration': 3},
            {'name': 'Étude d\'impact environnemental', 'start': 1.5, 'duration': 2.5},
            {'name': 'Étude technique détaillée', 'start': 2, 'duration': 3},
        ]
    },
    'phase2': {
        'name': 'Autorisations',
        'tasks': [
            {'name': 'Autorisation provisoire', 'start': 4, 'duration': 4},
            {'name': 'Autorisation définitive', 'start': 9, 'duration': 3},
            {'name': 'Permis de construire', 'start': 10, 'duration': 2},
            {'name': 'Convention raccordement', 'start': 10.5, 'duration': 1.5},
        ]
    },
    'phase3': {
        'name': 'Financement',
        'tasks': [
            {'name': 'Dossier SIMEST/Piano Mattei', 'start': 5, 'duration': 2},
            {'name': 'Instruction SIMEST', 'start': 7, 'duration': 4},
            {'name': 'Dossier Charte Investissement', 'start': 6, 'duration': 1.5},
            {'name': 'Due diligence bancaire', 'start': 9, 'duration': 2.5},
            {'name': 'Closing financier', 'start': 11.5, 'duration': 1},
        ]
    },
    'phase4': {
        'name': 'Construction',
        'tasks': [
            {'name': 'Appel d\'offres EPC', 'start': 8, 'duration': 3},
            {'name': 'Préparation site', 'start': 12.5, 'duration': 1.5},
            {'name': 'Génie civil', 'start': 14, 'duration': 2.5},
            {'name': 'Installation structures', 'start': 16, 'duration': 2},
            {'name': 'Installation modules PV', 'start': 17, 'duration': 3},
            {'name': 'Raccordement réseau', 'start': 19, 'duration': 1},
        ]
    },
    'phase5': {
        'name': 'Mise en service',
        'tasks': [
            {'name': 'Tests préliminaires', 'start': 20, 'duration': 0.5},
            {'name': 'Mise en service progressive', 'start': 20.5, 'duration': 1},
            {'name': 'Tests performance', 'start': 21.5, 'duration': 0.5},
            {'name': 'Réception commerciale (COD)', 'start': 22, 'duration': 0.5},
        ]
    }
}

# Jalons clés (milestones)
milestones = [
    {'name': 'Validation préfaisabilité', 'position': 2},
    {'name': 'Autorisation provisoire', 'position': 8},
    {'name': 'Bouclage financier', 'position': 12.5},
    {'name': 'Fin installation modules', 'position': 19},
    {'name': 'COD', 'position': 22.5},
]

# Tracer le diagramme
current_y = 0
task_positions = {}
all_tasks = []

# Préparer les données
for phase_id, phase in phases.items():
    for task in phase['tasks']:
        task['phase'] = phase_id
        task['y'] = current_y
        task_positions[task['name']] = current_y
        all_tasks.append(task)
        current_y += 1
    current_y += 0.5  # Espace entre les phases

# Définir les limites des axes
ax.set_xlim(-1, 24)
ax.set_ylim(-1, current_y)

# Tracer les barres des tâches
for task in all_tasks:
    # Barre principale avec effet 3D subtil
    ax.barh(task['y'], task['duration'], left=task['start'], height=0.6,
           color=colors[task['phase']], alpha=0.9, edgecolor='white',
           linewidth=0.8, zorder=3)
    
    # Ajout d'un léger ombrage en bas pour effet 3D
    ax.barh(task['y']-0.05, task['duration'], left=task['start'], height=0.1,
           color='black', alpha=0.1, zorder=2)
    
    # Ajout d'un léger éclat en haut pour effet 3D
    ax.barh(task['y']+0.15, task['duration'], left=task['start'], height=0.1,
           color='white', alpha=0.3, zorder=4)
    
    # Nom de la tâche
    ax.text(-0.2, task['y'], task['name'], ha='right', va='center',
           fontsize=9, color=colors['text'], fontweight='normal',
           bbox=dict(facecolor=colors['background'], edgecolor='none', pad=1, alpha=0.7))
    
    # Durée
    ax.text(task['start'] + task['duration'] + 0.1, task['y'],
           f"{task['duration']:.1f} mois", va='center', fontsize=8, color=colors['text'],
           fontweight='light', style='italic')

# Tracer les jalons
for i, milestone in enumerate(milestones):
    pos = milestone['position']
    # Alterner le placement des jalons (haut/bas) pour éviter les chevauchements
    if i % 2 == 0:
        y_offset = 2  # Position en haut
        va_setting = 'bottom'
    else:
        y_offset = -2  # Position en bas
        va_setting = 'top'
    
    # Ligne verticale pour le jalon
    ax.axvline(x=pos, ymin=0.1, ymax=0.9, color=colors['milestone_line'], linestyle='--', alpha=0.5, linewidth=1)
    
    # Marqueur du jalon
    ax.scatter(pos, y_offset, marker='D', s=100, color=colors['milestone'], zorder=5, edgecolor='white')
    
    # Texte du jalon
    ax.text(pos, y_offset + (0.8 if i % 2 == 0 else -0.8), milestone['name'], rotation=0,
           ha='center', va=va_setting, fontsize=9, weight='bold', color='#2c3e50',
           bbox=dict(facecolor='white', alpha=0.7, boxstyle='round,pad=0.3', edgecolor='none'))

# Ajouter les titres de phases
for i, (phase_id, phase) in enumerate(phases.items()):
    phase_tasks = [t for t in all_tasks if t['phase'] == phase_id]
    if phase_tasks:
        min_y = min(t['y'] for t in phase_tasks)
        max_y = max(t['y'] for t in phase_tasks)
        ax.text(-0.8, (min_y + max_y) / 2, phase['name'], rotation=90,
               ha='center', va='center', fontsize=10, weight='bold',
               color=colors[phase_id])

# Grille et axe des x
ax.grid(True, axis='x', linestyle='--', alpha=0.4, color=colors['grid'])
ax.set_xticks(range(0, 25, 3))
ax.set_xticklabels([f"M{i}" for i in range(0, 25, 3)])

# Ajout de lignes horizontales légères pour séparer les tâches
for i in range(len(all_tasks)):
    if i > 0 and all_tasks[i]['phase'] != all_tasks[i-1]['phase']:
        # Ligne plus visible pour séparer les phases
        ax.axhline(y=all_tasks[i]['y'] - 0.4, color=colors['border'], linestyle='-', alpha=0.5, linewidth=0.8)
    else:
        # Ligne légère entre les tâches
        ax.axhline(y=all_tasks[i]['y'] - 0.4, color=colors['grid'], linestyle=':', alpha=0.3, linewidth=0.5)

ax.set_xlabel('Mois depuis le début du projet', fontweight='bold', color=colors['text'])

# Supprimer les étiquettes de l'axe y
ax.set_yticks([])

# Légende
handles = [mpatches.Patch(color=colors[phase], label=phases[phase]['name']) for phase in phases]
plt.legend(handles=handles, loc='upper center', bbox_to_anchor=(0.5, -0.05),
          ncol=len(phases), frameon=True)

# Titre
plt.suptitle("CALENDRIER DE DÉVELOPPEMENT ET CONSTRUCTION", 
           fontsize=14, weight='bold', color=colors['text'], y=0.98)
plt.title("Projet Solaire PV 10 MW - Maroc", 
        fontsize=12, color=colors['text'], style='italic', pad=10)

# Annotation
plt.text(0.01, 0.01, "Durée totale: 24 mois", transform=fig.transFigure,
       fontsize=9, style='italic')

plt.tight_layout()

# Sauvegarder l'image
plt.savefig('figures/gantt_faisabilite.png', dpi=300, bbox_inches='tight', 
           facecolor=colors['background'])
print("Diagramme de Gantt simplifié généré avec succès!")
plt.close()
