from __future__ import annotations

import math
from dataclasses import asdict
from typing import Dict, List
from pathlib import Path

import numpy as np
import pandas as pd
import xlsxwriter

try:
    import numpy_financial as npf
except ImportError:  # minimalist fallback if package missing

    def _npv(rate, values):
        return sum(v / (1 + rate) ** i for i, v in enumerate(values))

    def irr(values, tol=1e-6, maxiter=100):
        rate = 0.1
        for _ in range(maxiter):
            f = _npv(rate, values)
            df = sum(-i * v / (1 + rate) ** (i + 1) for i, v in enumerate(values))
            rate_new = rate - f / df if df else rate
            if abs(rate_new - rate) < tol:
                return rate_new
            rate = rate_new
        return rate

    class npf:  # type: ignore
        irr = staticmethod(irr)
        npv = staticmethod(_npv)

from .data_models import Assumptions

def build_cashflow(a: Assumptions) -> pd.DataFrame:
    yrs = np.arange(0, a.years + 1)
    df = pd.DataFrame(index=yrs)

    # CAPEX & grants (year 0 only)
    df["Capex"] = 0.0
    df.loc[0, "Capex"] = -a.capex_meur * 1e6
    df["Grants"] = 0.0
    df.loc[0, "Grants"] = a.total_grants_meur * 1e6

    # Production & price series
    prod = [0] + [a.production_mwh_year1 * (1 - a.degradation) ** (y - 1) for y in yrs if y]
    price = [0] + [a.ppa_price_eur_kwh * (1 + a.price_escalation) ** (y - 1) for y in yrs if y]
    df["Prod_MWh"] = prod
    df["Price_EUR_kWh"] = price
    df["Revenue"] = df["Prod_MWh"] * 1_000 * df["Price_EUR_kWh"]

    # OPEX
    opex = [0] + [a.opex_keuros_year1 * 1e3 * (1 + a.opex_escalation) ** (y - 1) for y in yrs if y]
    df["OPEX"] = [-v for v in opex]

    # EBITDA
    df["EBITDA"] = df["Revenue"] + df["OPEX"]

    # Debt schedule
    debt_nom = a.investment_for_debt_sizing_meur * 1e6 * a.debt_ratio
    debt_nom = max(0, debt_nom)
    yearly_principal = debt_nom / (a.debt_years - a.grace_years) if (a.debt_years - a.grace_years) > 0 else 0
    outstanding = debt_nom
    int_col, prin_col = [0.0], [0.0]
    for y in yrs[1:]:
        interest = -outstanding * a.interest_rate if outstanding else 0.0
        if y <= a.grace_years:
            principal = 0.0
        else:
            principal = -min(yearly_principal, outstanding)
        outstanding += principal  # principal is negative
        int_col.append(interest)
        prin_col.append(principal)
    df["Interest"] = int_col
    df["Principal"] = prin_col
    df["Debt_Service"] = df["Interest"] + df["Principal"]

    # Taxes
    taxable = df["EBITDA"] + df["Interest"]  # interest deductible
    tax = [-max(0, v * a.tax_rate) if y > a.tax_holiday else 0 for y, v in taxable.items()]
    df["Tax"] = tax

    # Net cashflow to firm
    df["Net_CF_Firm"] = df[["EBITDA", "Interest", "Principal", "Tax"]].sum(axis=1)

    # Equity cashflows (include initial equity injection)
    debt_draw = -debt_nom  # positive at t=0
    df["Equity_CF"] = df["Net_CF_Firm"].copy()
    df.loc[0, "Equity_CF"] += df.loc[0, "Capex"] - debt_draw
    df.loc[0, "Equity_CF"] += df.loc[0, "Grants"]  # grants benefit equity

    # DSCR (avoid div/0)
    dscr = df["EBITDA"] / df["Debt_Service"].replace(0, np.nan)
    df["DSCR"] = dscr

    return df.fillna(0)


def compute_kpis(df: pd.DataFrame, a: Assumptions) -> Dict[str, float]:
    eq_cf = df["Equity_CF"].values.astype(float)
    irr = npf.irr(eq_cf)
    npv = npf.npv(a.discount_rate, eq_cf)

    # LCOE discounted (cost side only)
    disc = (1 / (1 + a.discount_rate) ** df.index).values
    cost = -(df["Capex"] + df["OPEX"]).values * disc
    energy = (df["Prod_MWh"] * 1_000).values * disc
    lcoe = cost.sum() / energy.sum() if energy.sum() else math.nan

    return {
        "IRR_equity": irr,
        "NPV_equity": npv,
        "LCOE_eur_kwh": lcoe,
        "Min_DSCR": df["DSCR"].replace([np.inf, -np.inf], np.nan).min(),
    }


def build_sensitivity(a: Assumptions, var: str, deltas: List[float] = (-0.1, -0.05, 0.05, 0.1)) -> pd.DataFrame:
    base_irr = compute_kpis(build_cashflow(a), a)["IRR_equity"]
    rows = []
    for d in deltas:
        a_mod = Assumptions(**asdict(a))
        setattr(a_mod, var, getattr(a_mod, var) * (1 + d))
        irr = compute_kpis(build_cashflow(a_mod), a_mod)["IRR_equity"]
        rows.append({"Scenario": f"{var} {d:+.0%}", "IRR_equity": irr, "Δ vs base": irr - base_irr})
    return pd.DataFrame(rows)


def run_scenarios() -> Dict[str, Dict[str, float]]:
    """Run scenarios for both Ouarzazate and Dakhla, with and without MASEN/IRESEN incentives"""
    results = {}
    
    # Base case - Ouarzazate (using default values which are Ouarzazate-like)
    a_ouar = Assumptions(location_name="Ouarzazate") # Defaults are Ouarzazate-like
    cf_ouar = build_cashflow(a_ouar)
    results["Ouarzazate (Defaults)"] = compute_kpis(cf_ouar, a_ouar)
    
    # Example for Dakhla - requires explicitly setting the different parameters
    a_dakhla = Assumptions(
        location_name="Dakhla",
        production_mwh_year1=19_200,  # Example Dakhla value
        opex_keuros_year1=310,       # Example Dakhla value
        capex_meur=9.8,              # Example Dakhla value
        # Other Dakhla-specific assumptions if any, otherwise defaults are used
        # For grant_meur_masen, etc., if they differ for Dakhla, they'd be set here too.
        # E.g., grant_meur_masen = 0 if Dakhla is not a priority zone for this grant.
    )
    cf_dakhla = build_cashflow(a_dakhla)
    results["Dakhla (Example Values)"] = compute_kpis(cf_dakhla, a_dakhla)
    
    # Stress case - Ouarzazate (modifying default Ouarzazate-like values)
    a_ouar_stress = Assumptions(
        location_name="Ouarzazate (Stress)",
        production_mwh_year1=Assumptions().production_mwh_year1 * 0.95, # Based on default Ouarzazate
        capex_meur=Assumptions().capex_meur * 1.1,                  # Based on default Ouarzazate
        ppa_price_eur_kwh=Assumptions().ppa_price_eur_kwh * 0.9,
    )
    cf_ouar_stress = build_cashflow(a_ouar_stress)
    results["Ouarzazate (Stress)"] = compute_kpis(cf_ouar_stress, a_ouar_stress)
    
    # Stress case - Ouarzazate WITHOUT specific Moroccan incentives
    a_ouar_stress_no_moroccan_incent = Assumptions(
        location_name="Ouarzazate (Stress, No Masen/Iresen/Connection Grants)",
        production_mwh_year1=Assumptions().production_mwh_year1 * 0.95,
        capex_meur=Assumptions().capex_meur * 1.1,
        ppa_price_eur_kwh=Assumptions().ppa_price_eur_kwh * 0.9,
        grant_meur_masen=0.0,
        grant_meur_iresen=0.0,
        grant_meur_connection=0.0,
        # Italian grant could still apply, or set grant_meur_italy=0.0 too if all grants removed
    )
    cf_ouar_stress_no_moroccan_incent = build_cashflow(a_ouar_stress_no_moroccan_incent)
    results["Ouarzazate (Stress, No Masen/Iresen/Connection Grants)"] = compute_kpis(cf_ouar_stress_no_moroccan_incent, a_ouar_stress_no_moroccan_incent)
    
    return results

# Added for Flet app integration
from dataclasses import asdict # Make sure asdict is imported
from typing import Dict # Ensure Dict is imported for type hinting

def export_excel(a: Assumptions, cf: pd.DataFrame, kpis: Dict[str, float], sens: pd.DataFrame, dest: Path):
    """Exports the financial model data (inputs, cashflow, KPIs, sensitivity) to an Excel file."""
    with pd.ExcelWriter(dest, engine="xlsxwriter") as writer:
        # Inputs (using current assumptions used for the model run)
        # Create a DataFrame from the asdict(a) for inputs
        inputs_df = pd.DataFrame(asdict(a).items(), columns=["Assumption", "Value"])
        inputs_df.to_excel(writer, sheet_name="Inputs", index=False)

        # Cashflow
        cf.to_excel(writer, sheet_name="Cashflow")

        # KPIs
        pd.DataFrame(list(kpis.items()), columns=["KPI", "Value"]).to_excel(writer, sheet_name="KPIs", index=False)

        # Sensitivity
        sens.to_excel(writer, sheet_name="Sensitivity", index=False)

        # Formatting (simple example, can be expanded)
        workbook = writer.book
        fmt_pct = workbook.add_format({"num_format": "0.0%"})
        fmt_eur_kwh = workbook.add_format({"num_format": "#,##0.000 €/kWh"})
        fmt_eur = workbook.add_format({"num_format": "#,##0 €"})
        fmt_float = workbook.add_format({"num_format": "0.00"})

        # Apply formatting to relevant sheets and columns
        # Inputs sheet: Format values based on assumption name or type if possible (more complex)
        # For now, no specific formatting on inputs values beyond default.

        # Cashflow sheet: Format currency columns, DSCR
        for col_name in ["Capex", "Grants", "Revenue", "OPEX", "EBITDA", "Interest", "Principal", "Debt_Service", "Net_CF_Firm", "Equity_CF"]:
            if col_name in cf.columns:
                # Find column letter
                col_idx = cf.columns.get_loc(col_name) + 1 # +1 for index col if cf is written with index
                # If cf is written without index, it's just cf.columns.get_loc(col_name)
                # Assuming cf.to_excel writes the index, so +1
                # Let's get the column letter from xlsxwriter utility
                col_letter = xlsxwriter.utility.xl_col_to_name(cf.columns.get_loc(col_name) + (1 if cf.index.name is not None or cf.index.name == "Année" else 0) ) 
                writer.sheets["Cashflow"].set_column(f"{col_letter}:{col_letter}", 15, fmt_eur) 
        if "DSCR" in cf.columns:
            col_letter = xlsxwriter.utility.xl_col_to_name(cf.columns.get_loc("DSCR") + (1 if cf.index.name is not None or cf.index.name == "Année" else 0) )
            writer.sheets["Cashflow"].set_column(f"{col_letter}:{col_letter}", 12, fmt_float)
        if "Price_EUR_kWh" in cf.columns:
            col_letter = xlsxwriter.utility.xl_col_to_name(cf.columns.get_loc("Price_EUR_kWh") + (1 if cf.index.name is not None or cf.index.name == "Année" else 0) )
            writer.sheets["Cashflow"].set_column(f"{col_letter}:{col_letter}", 15, fmt_eur_kwh)


        # KPIs sheet: Format IRR, LCOE, NPV, DSCR
        kpi_sheet = writer.sheets["KPIs"]
        kpi_df_for_fmt = pd.DataFrame(list(kpis.items()), columns=["KPI", "Value"])
        for row_num, (kpi_name, kpi_value) in enumerate(kpi_df_for_fmt.values, 1): # 1 for header
            if "IRR" in kpi_name:
                kpi_sheet.write_number(row_num, 1, kpi_value, fmt_pct)
            elif "LCOE" in kpi_name:
                kpi_sheet.write_number(row_num, 1, kpi_value, fmt_eur_kwh)
            elif "NPV" in kpi_name:
                kpi_sheet.write_number(row_num, 1, kpi_value, fmt_eur)
            elif "DSCR" in kpi_name:
                kpi_sheet.write_number(row_num, 1, kpi_value, fmt_float)
            # else use default format
        kpi_sheet.set_column("A:A", 25) # KPI names
        kpi_sheet.set_column("B:B", 18) # Values

        # Sensitivity sheet: Format IRR columns
        sens_sheet = writer.sheets["Sensitivity"]
        if "IRR_equity" in sens.columns:
            col_letter = xlsxwriter.utility.xl_col_to_name(sens.columns.get_loc("IRR_equity"))
            sens_sheet.set_column(f"{col_letter}:{col_letter}", 15, fmt_pct)
        if "Δ vs base" in sens.columns:
            col_letter = xlsxwriter.utility.xl_col_to_name(sens.columns.get_loc("Δ vs base"))
            sens_sheet.set_column(f"{col_letter}:{col_letter}", 15, fmt_pct)
        sens_sheet.set_column("A:A", 25) # Scenario names

        print(f"Excel file saved to {dest}") 