from __future__ import annotations

import math
from dataclasses import asdict, dataclass
from typing import Dict, List, Tuple, Optional
from pathlib import Path

import numpy as np
import pandas as pd
import xlsxwriter
from scipy import stats

try:
    import numpy_financial as npf
except ImportError:
    def _npv(rate, values):
        return sum(v / (1 + rate) ** i for i, v in enumerate(values))

    def irr(values, tol=1e-6, maxiter=100):
        rate = 0.1
        for _ in range(maxiter):
            f = _npv(rate, values)
            df = sum(-i * v / (1 + rate) ** (i + 1) for i, v in enumerate(values))
            rate_new = rate - f / df if df else rate
            if abs(rate_new - rate) < tol:
                return rate_new
            rate = rate_new
        return rate

    class npf:
        irr = staticmethod(irr)
        npv = staticmethod(_npv)

from .data_models import Assumptions

@dataclass
class EnhancedAssumptions(Assumptions):
    """Enhanced assumptions with additional parameters for professional modeling"""
    
    # Terminal value parameters
    terminal_growth_rate: float = 0.025  # 2.5% perpetual growth
    exit_multiple_ebitda: float = 8.0    # EV/EBITDA exit multiple
    use_terminal_value: bool = True       # Whether to include terminal value
    terminal_method: str = "perpetuity"   # "perpetuity" or "exit_multiple"
    
    # Enhanced operational parameters
    insurance_rate: float = 0.003        # Insurance as % of CAPEX annually
    land_lease_eur_mw_year: float = 2000 # Land lease cost per MW per year
    capacity_mw: float = 10.0            # Plant capacity in MW
    
    # Performance degradation (more sophisticated)
    degradation_year1: float = 0.025     # First year degradation
    degradation_annual: float = 0.005    # Annual degradation after year 1
    
    # Enhanced financial parameters
    working_capital_days: int = 30       # Working capital in days of revenue
    corporate_tax_rate: float = 0.31     # Morocco corporate tax rate
    withholding_tax_rate: float = 0.10   # Withholding tax on dividends
    
    # Risk parameters for Monte Carlo
    production_volatility: float = 0.05  # Standard deviation of production
    price_volatility: float = 0.03       # Standard deviation of price
    capex_volatility: float = 0.10       # Standard deviation of CAPEX
    
    # Corrected grant structure (removing IRESEN for commercial projects)
    grant_meur_iresen: float = 0.0       # Set to 0 for commercial projects
    
    # Note: total_grant_meur_maroc, total_grants_meur, and investment_for_debt_sizing_meur
    # are calculated as properties in the base Assumptions class

def build_enhanced_cashflow(a: EnhancedAssumptions) -> pd.DataFrame:
    """Build enhanced cashflow with terminal value and improved modeling"""
    yrs = np.arange(0, a.years + 1)
    df = pd.DataFrame(index=yrs)

    # CAPEX & grants (year 0 only)
    df["Capex"] = 0.0
    df.loc[0, "Capex"] = -a.capex_meur * 1e6
    df["Grants"] = 0.0
    df.loc[0, "Grants"] = a.total_grants_meur * 1e6

    # Enhanced production with more realistic degradation
    prod = [0]
    for y in yrs[1:]:
        if y == 1:
            prod_y = a.production_mwh_year1 * (1 - a.degradation_year1)
        else:
            prod_y = prod[-1] * (1 - a.degradation_annual)
        prod.append(prod_y)
    
    # Price escalation
    price = [0] + [a.ppa_price_eur_kwh * (1 + a.price_escalation) ** (y - 1) for y in yrs if y]
    
    df["Prod_MWh"] = prod
    df["Price_EUR_kWh"] = price
    df["Revenue"] = df["Prod_MWh"] * 1_000 * df["Price_EUR_kWh"]

    # Enhanced OPEX including insurance and land lease
    base_opex = [0] + [a.opex_keuros_year1 * 1e3 * (1 + a.opex_escalation) ** (y - 1) for y in yrs if y]
    insurance = [0] + [a.capex_meur * 1e6 * a.insurance_rate for y in yrs if y]
    land_lease = [0] + [a.capacity_mw * a.land_lease_eur_mw_year for y in yrs if y]
    
    total_opex = [base + ins + land for base, ins, land in zip(base_opex, insurance, land_lease)]
    df["OPEX"] = [-v for v in total_opex]
    df["Insurance"] = [-v for v in insurance]
    df["Land_Lease"] = [-v for v in land_lease]

    # Working capital changes
    wc = df["Revenue"] * a.working_capital_days / 365
    wc_change = wc.diff().fillna(wc.iloc[0] if len(wc) > 0 else 0)
    df["WC_Change"] = -wc_change  # Negative because increase in WC is cash outflow

    # EBITDA
    df["EBITDA"] = df["Revenue"] + df["OPEX"]

    # Debt schedule (unchanged from original)
    debt_nom = a.investment_for_debt_sizing_meur * 1e6 * a.debt_ratio
    debt_nom = max(0, debt_nom)
    yearly_principal = debt_nom / (a.debt_years - a.grace_years) if (a.debt_years - a.grace_years) > 0 else 0
    outstanding = debt_nom
    int_col, prin_col = [0.0], [0.0]
    for y in yrs[1:]:
        interest = -outstanding * a.interest_rate if outstanding else 0.0
        if y <= a.grace_years:
            principal = 0.0
        else:
            principal = -min(yearly_principal, outstanding)
        outstanding += principal
        int_col.append(interest)
        prin_col.append(principal)
    df["Interest"] = int_col
    df["Principal"] = prin_col
    df["Debt_Service"] = df["Interest"] + df["Principal"]

    # Enhanced tax calculation
    taxable = df["EBITDA"] + df["Interest"]
    tax = [-max(0, v * a.corporate_tax_rate) if y > a.tax_holiday else 0 for y, v in taxable.items()]
    df["Tax"] = tax

    # Free Cash Flow to Firm (before terminal value)
    df["FCF_Firm"] = df["EBITDA"] + df["Interest"] + df["Tax"] + df["WC_Change"]

    # Terminal value calculation
    if a.use_terminal_value and a.years > 0:
        terminal_value = calculate_terminal_value(df, a)
        df.loc[a.years, "Terminal_Value"] = terminal_value
        df["Terminal_Value"] = df["Terminal_Value"].fillna(0)
    else:
        df["Terminal_Value"] = 0

    # Total Free Cash Flow to Firm (including terminal value)
    df["Total_FCF_Firm"] = df["FCF_Firm"] + df["Terminal_Value"]

    # Equity cashflows
    debt_draw = -debt_nom
    df["Equity_CF"] = df["Total_FCF_Firm"] - df["Principal"]
    df.loc[0, "Equity_CF"] += df.loc[0, "Capex"] - debt_draw + df.loc[0, "Grants"]

    # DSCR
    dscr = df["EBITDA"] / df["Debt_Service"].replace(0, np.nan)
    df["DSCR"] = dscr

    return df.fillna(0)

def calculate_terminal_value(df: pd.DataFrame, a: EnhancedAssumptions) -> float:
    """Calculate terminal value using perpetuity growth or exit multiple method"""
    if a.terminal_method == "perpetuity":
        # Perpetuity growth method
        final_fcf = df.loc[a.years, "FCF_Firm"]
        if a.discount_rate <= a.terminal_growth_rate:
            return 0  # Avoid negative or infinite values
        terminal_value = (final_fcf * (1 + a.terminal_growth_rate)) / (a.discount_rate - a.terminal_growth_rate)
        return max(0, terminal_value)
    
    elif a.terminal_method == "exit_multiple":
        # Exit multiple method
        final_ebitda = df.loc[a.years, "EBITDA"]
        terminal_value = final_ebitda * a.exit_multiple_ebitda
        return max(0, terminal_value)
    
    return 0

def compute_enhanced_kpis(df: pd.DataFrame, a: EnhancedAssumptions) -> Dict[str, float]:
    """Compute enhanced KPIs including LCOE and additional metrics"""
    eq_cf = df["Equity_CF"].values.astype(float)
    firm_cf = df["Total_FCF_Firm"].values.astype(float)
    
    # IRR and NPV calculations
    try:
        irr_equity = npf.irr(eq_cf)
        irr_project = npf.irr(firm_cf)
    except:
        irr_equity = np.nan
        irr_project = np.nan
    
    npv_equity = npf.npv(a.discount_rate, eq_cf)
    npv_project = npf.npv(a.discount_rate, firm_cf)

    # LCOE calculation (enhanced)
    disc_factors = (1 / (1 + a.discount_rate) ** df.index).values
    
    # Total costs (CAPEX + OPEX, excluding grants)
    total_costs = -(df["Capex"] + df["OPEX"] + df["Insurance"] + df["Land_Lease"]).values
    discounted_costs = (total_costs * disc_factors).sum()
    
    # Total energy production
    total_energy_kwh = (df["Prod_MWh"] * 1_000).values
    discounted_energy = (total_energy_kwh * disc_factors).sum()
    
    lcoe = discounted_costs / discounted_energy if discounted_energy > 0 else np.nan

    # Additional financial metrics
    min_dscr = df["DSCR"].replace([np.inf, -np.inf], np.nan).min()
    avg_dscr = df["DSCR"].replace([np.inf, -np.inf], np.nan).mean()
    
    # Payback period (simple)
    cumulative_cf = eq_cf.cumsum()
    payback_years = np.nan
    positive_cf_indices = np.where(cumulative_cf > 0)[0]
    if len(positive_cf_indices) > 0:
        payback_years = positive_cf_indices[0]
    
    # Return on equity
    initial_equity = -eq_cf[0] if eq_cf[0] < 0 else 0
    total_equity_returns = eq_cf[1:].sum()
    roe = total_equity_returns / initial_equity if initial_equity > 0 else np.nan

    return {
        "IRR_equity": irr_equity,
        "IRR_project": irr_project,
        "NPV_equity": npv_equity,
        "NPV_project": npv_project,
        "LCOE_eur_kwh": lcoe,
        "Min_DSCR": min_dscr,
        "Avg_DSCR": avg_dscr,
        "Payback_years": payback_years,
        "ROE": roe,
        "Debt_to_equity_ratio": a.debt_ratio / (1 - a.debt_ratio),
        "Grant_percentage": (a.total_grants_meur / a.capex_meur) * 100,
    }

def build_enhanced_sensitivity(a: EnhancedAssumptions, variables: List[str], 
                             deltas: List[float] = [-0.2, -0.1, -0.05, 0.05, 0.1, 0.2]) -> pd.DataFrame:
    """Enhanced sensitivity analysis with multiple variables"""
    base_kpis = compute_enhanced_kpis(build_enhanced_cashflow(a), a)
    base_irr = base_kpis["IRR_equity"]
    base_npv = base_kpis["NPV_equity"]
    
    results = []
    
    for var in variables:
        for delta in deltas:
            a_mod = EnhancedAssumptions(**asdict(a))
            original_value = getattr(a_mod, var)
            setattr(a_mod, var, original_value * (1 + delta))
            
            try:
                kpis = compute_enhanced_kpis(build_enhanced_cashflow(a_mod), a_mod)
                irr = kpis["IRR_equity"]
                npv = kpis["NPV_equity"]
                
                results.append({
                    "Variable": var,
                    "Delta": f"{delta:+.1%}",
                    "IRR_equity": irr,
                    "NPV_equity": npv,
                    "IRR_change": irr - base_irr,
                    "NPV_change": npv - base_npv,
                    "IRR_sensitivity": (irr - base_irr) / (delta * base_irr) if base_irr != 0 else np.nan
                })
            except Exception as e:
                results.append({
                    "Variable": var,
                    "Delta": f"{delta:+.1%}",
                    "IRR_equity": np.nan,
                    "NPV_equity": np.nan,
                    "IRR_change": np.nan,
                    "NPV_change": np.nan,
                    "IRR_sensitivity": np.nan
                })
    
    return pd.DataFrame(results)

def monte_carlo_simulation(a: EnhancedAssumptions, n_simulations: int = 1000) -> Dict[str, np.ndarray]:
    """Monte Carlo simulation for risk analysis"""
    results = {
        "IRR_equity": [],
        "NPV_equity": [],
        "LCOE_eur_kwh": [],
        "Min_DSCR": []
    }
    
    for _ in range(n_simulations):
        # Create random variations
        a_sim = EnhancedAssumptions(**asdict(a))
        
        # Apply random variations to key parameters
        a_sim.production_mwh_year1 *= (1 + np.random.normal(0, a.production_volatility))
        a_sim.ppa_price_eur_kwh *= (1 + np.random.normal(0, a.price_volatility))
        a_sim.capex_meur *= (1 + np.random.normal(0, a.capex_volatility))
        
        # Ensure positive values
        a_sim.production_mwh_year1 = max(0, a_sim.production_mwh_year1)
        a_sim.ppa_price_eur_kwh = max(0, a_sim.ppa_price_eur_kwh)
        a_sim.capex_meur = max(0, a_sim.capex_meur)
        
        # Properties are automatically calculated, no need for __post_init__()
        
        try:
            cf = build_enhanced_cashflow(a_sim)
            kpis = compute_enhanced_kpis(cf, a_sim)
            
            for key in results.keys():
                results[key].append(kpis[key])
        except:
            # Handle failed simulations
            for key in results.keys():
                results[key].append(np.nan)
    
    # Convert to numpy arrays and remove NaN values
    for key in results.keys():
        arr = np.array(results[key])
        results[key] = arr[~np.isnan(arr)]
    
    return results

def generate_monte_carlo_statistics(mc_results: Dict[str, np.ndarray]) -> Dict[str, Dict[str, float]]:
    """Generate statistics from Monte Carlo results"""
    statistics = {}
    
    for metric, values in mc_results.items():
        if len(values) > 0:
            statistics[metric] = {
                "mean": np.mean(values),
                "std": np.std(values),
                "min": np.min(values),
                "max": np.max(values),
                "p5": np.percentile(values, 5),
                "p25": np.percentile(values, 25),
                "p50": np.percentile(values, 50),
                "p75": np.percentile(values, 75),
                "p95": np.percentile(values, 95),
                "var_coeff": np.std(values) / np.mean(values) if np.mean(values) != 0 else np.nan
            }
        else:
            statistics[metric] = {key: np.nan for key in ["mean", "std", "min", "max", "p5", "p25", "p50", "p75", "p95", "var_coeff"]}
    
    return statistics

def run_enhanced_scenarios() -> Dict[str, Dict[str, float]]:
    """Run enhanced scenarios with corrected grant assumptions"""
    results = {}
    
    # Base case - Ouarzazate with corrected grants
    a_base = EnhancedAssumptions(
        location_name="Ouarzazate_Enhanced",
        # Corrected grant structure (no IRESEN for commercial projects)
        grant_meur_italy=1.2,      # Italian government support
        grant_meur_masen=0.8,      # MASEN support for strategic projects
        grant_meur_iresen=0.0,     # No IRESEN for commercial projects
        grant_meur_connection=0.3, # Grid connection subsidies
        use_terminal_value=True,
        terminal_method="perpetuity"
    )
    
    cf_base = build_enhanced_cashflow(a_base)
    results["Base_Case_Enhanced"] = compute_enhanced_kpis(cf_base, a_base)
    
    # Conservative case with higher WACC
    a_conservative = EnhancedAssumptions(**asdict(a_base))
    a_conservative.location_name = "Conservative_Scenario"
    a_conservative.discount_rate = 0.12  # Higher WACC for emerging markets
    a_conservative.production_mwh_year1 *= 0.95  # Lower production
    a_conservative.capex_meur *= 1.15  # Higher CAPEX
    
    cf_conservative = build_enhanced_cashflow(a_conservative)
    results["Conservative_Scenario"] = compute_enhanced_kpis(cf_conservative, a_conservative)
    
    # Optimistic case
    a_optimistic = EnhancedAssumptions(**asdict(a_base))
    a_optimistic.location_name = "Optimistic_Scenario"
    a_optimistic.discount_rate = 0.08  # Lower WACC
    a_optimistic.production_mwh_year1 *= 1.05  # Higher production
    a_optimistic.capex_meur *= 0.90  # Lower CAPEX
    a_optimistic.ppa_price_eur_kwh *= 1.05  # Higher PPA price
    
    cf_optimistic = build_enhanced_cashflow(a_optimistic)
    results["Optimistic_Scenario"] = compute_enhanced_kpis(cf_optimistic, a_optimistic)
    
    # No grants scenario
    a_no_grants = EnhancedAssumptions(**asdict(a_base))
    a_no_grants.location_name = "No_Grants_Scenario"
    a_no_grants.grant_meur_italy = 0.0
    a_no_grants.grant_meur_masen = 0.0
    a_no_grants.grant_meur_connection = 0.0
    # Properties are automatically calculated, no need for __post_init__()
    
    cf_no_grants = build_enhanced_cashflow(a_no_grants)
    results["No_Grants_Scenario"] = compute_enhanced_kpis(cf_no_grants, a_no_grants)
    
    return results

def export_enhanced_excel(scenarios: Dict[str, Dict[str, float]], 
                         sensitivity_df: pd.DataFrame,
                         mc_stats: Dict[str, Dict[str, float]],
                         filename: str = "enhanced_financial_model.xlsx") -> None:
    """Export enhanced results to Excel with professional formatting"""
    
    with pd.ExcelWriter(filename, engine='xlsxwriter') as writer:
        workbook = writer.book
        
        # Define formats
        header_format = workbook.add_format({
            'bold': True,
            'text_wrap': True,
            'valign': 'top',
            'fg_color': '#D7E4BC',
            'border': 1
        })
        
        currency_format = workbook.add_format({
            'num_format': '€#,##0',
            'border': 1
        })
        
        percentage_format = workbook.add_format({
            'num_format': '0.00%',
            'border': 1
        })
        
        number_format = workbook.add_format({
            'num_format': '#,##0.00',
            'border': 1
        })
        
        # Scenarios summary
        scenarios_df = pd.DataFrame(scenarios).T
        scenarios_df.to_excel(writer, sheet_name='Scenarios_Summary', startrow=1)
        
        worksheet = writer.sheets['Scenarios_Summary']
        worksheet.write('A1', 'Enhanced Financial Model - Scenarios Summary', header_format)
        
        # Sensitivity analysis
        sensitivity_df.to_excel(writer, sheet_name='Sensitivity_Analysis', index=False)
        
        # Monte Carlo statistics
        mc_df = pd.DataFrame(mc_stats).T
        mc_df.to_excel(writer, sheet_name='Monte_Carlo_Stats', startrow=1)
        
        worksheet_mc = writer.sheets['Monte_Carlo_Stats']
        worksheet_mc.write('A1', 'Monte Carlo Simulation Statistics', header_format)
        
        # Model assumptions and validation notes
        validation_notes = [
            "ENHANCED FINANCIAL MODEL - VALIDATION NOTES",
            "",
            "1. GRANT STRUCTURE CORRECTIONS:",
            "   - IRESEN grants removed for commercial projects (research-focused only)",
            "   - Italian government support validated through Mattei Plan",
            "   - MASEN grants for strategic renewable projects",
            "   - Grid connection subsidies included",
            "",
            "2. WACC BENCHMARKS:",
            "   - Base case: 10% (aligned with developed market standards)",
            "   - Conservative: 12% (emerging market premium)",
            "   - Optimistic: 8% (best-case financing)",
            "",
            "3. TERMINAL VALUE:",
            "   - Perpetuity growth method: 2.5% (aligned with long-term inflation)",
            "   - Exit multiple method: 8x EBITDA (industry benchmark)",
            "",
            "4. ENHANCED FEATURES:",
            "   - Monte Carlo simulation for risk analysis",
            "   - Enhanced sensitivity analysis",
            "   - Professional LCOE calculation",
            "   - Working capital modeling",
            "   - Insurance and land lease costs",
            "",
            "5. REGULATORY COMPLIANCE:",
            "   - Morocco corporate tax rate: 31%",
            "   - Withholding tax on dividends: 10%",
            "   - Enhanced degradation modeling",
            "",
            "6. RECOMMENDATIONS:",
            "   - Validate grant assumptions with official documentation",
            "   - Update WACC based on current market conditions",
            "   - Consider currency hedging for EUR-denominated revenues",
            "   - Regular model updates with actual performance data"
        ]
        
        validation_df = pd.DataFrame(validation_notes, columns=['Validation_Notes'])
        validation_df.to_excel(writer, sheet_name='Model_Validation', index=False, header=False)

# Integration function for the main application
def get_enhanced_model_results(assumptions_dict: Dict) -> Dict:
    """Main function to get enhanced model results for integration with Flet app"""
    
    # Convert dictionary to EnhancedAssumptions object
    a = EnhancedAssumptions(**assumptions_dict)
    
    # Build cashflow and compute KPIs
    cf = build_enhanced_cashflow(a)
    kpis = compute_enhanced_kpis(cf, a)
    
    # Run sensitivity analysis
    sensitivity_vars = ['production_mwh_year1', 'ppa_price_eur_kwh', 'capex_meur', 'discount_rate']
    sensitivity_df = build_enhanced_sensitivity(a, sensitivity_vars)
    
    # Run Monte Carlo simulation (smaller sample for performance)
    mc_results = monte_carlo_simulation(a, n_simulations=500)
    mc_stats = generate_monte_carlo_statistics(mc_results)
    
    # Run scenarios
    scenarios = run_enhanced_scenarios()
    
    return {
        'cashflow': cf.to_dict(),
        'kpis': kpis,
        'sensitivity': sensitivity_df.to_dict(),
        'monte_carlo_stats': mc_stats,
        'scenarios': scenarios,
        'assumptions': asdict(a)
    }