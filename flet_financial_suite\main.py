import flet as ft
from flet.matplotlib_chart import MatplotlibChart
import matplotlib
matplotlib.use('agg')
import matplotlib.pyplot as plt
import pandas as pd
from pathlib import Path
import math

from core.data_models import Assumptions
from core.financial_model_logic import build_cashflow, compute_kpis, build_sensitivity, export_excel as export_excel_logic
from core.figure_generator_logic import (
    generate_fcfe_cumule_fig,
    generate_dscr_fig,
    generate_sens_tri_fig,
    generate_structure_financement_pie_fig,
    generate_scenario_comparison_fig,
    generate_gantt_chart_fig,
    generate_lcoe_waterfall_fig
)

DEFAULT_PROFILES = {
    "Ouarzazate": {
        "production_mwh_year1": 18_000, "opex_keuros_year1": 285, "capex_meur": 9.5,
        "grant_meur_masen": 0.95, "grant_meur_iresen": 0.475, "grant_meur_connection": 0.285, "grant_meur_italy": 0.95
    },
    "Dakhla": {
        "production_mwh_year1": 19_200, "opex_keuros_year1": 310, "capex_meur": 9.8,
        "grant_meur_masen": 0.0, "grant_meur_iresen": 0.475, "grant_meur_connection": 0.300, "grant_meur_italy": 0.95
    },
    "Custom/Manual": { # Placeholder values for custom
        "production_mwh_year1": 10_000, "opex_keuros_year1": 200, "capex_meur": 8.0,
        "grant_meur_masen": 0.0, "grant_meur_iresen": 0.0, "grant_meur_connection": 0.0, "grant_meur_italy": 0.0
    }
}

initial_profile_data = DEFAULT_PROFILES["Ouarzazate"]
current_assumptions = Assumptions(
    location_name="Ouarzazate",
    production_mwh_year1=initial_profile_data["production_mwh_year1"],
    opex_keuros_year1=initial_profile_data["opex_keuros_year1"],
    capex_meur=initial_profile_data["capex_meur"],
    grant_meur_masen=initial_profile_data["grant_meur_masen"],
    grant_meur_iresen=initial_profile_data["grant_meur_iresen"],
    grant_meur_connection=initial_profile_data["grant_meur_connection"],
    grant_meur_italy=initial_profile_data["grant_meur_italy"]
)

latest_cash_flow_df = None
latest_kpis = None
latest_sensitivity_df = None
current_fig_generator = None
current_fig_args = ()
current_fig_title = ""
selected_output_path = None

# --- Global UI Element References (assigned in main) ---
page_ref: ft.Page = None
selected_folder_text_ref: ft.Text = None
kpi_display_ref: ft.Text = None
chart_display: MatplotlibChart = None
location_profile_dropdown: ft.Dropdown = None
ppa_price_input: ft.TextField = None
prod_input: ft.TextField = None
opex_input: ft.TextField = None
capex_input: ft.TextField = None
grant_masen_input: ft.TextField = None
grant_iresen_input: ft.TextField = None
grant_connection_input: ft.TextField = None
grant_italy_input: ft.TextField = None
years_input: ft.TextField = None
degradation_input: ft.TextField = None
price_escalation_input: ft.TextField = None
opex_escalation_input: ft.TextField = None
debt_ratio_input: ft.TextField = None
interest_rate_input: ft.TextField = None
debt_years_input: ft.TextField = None
grace_years_input: ft.TextField = None
tax_holiday_input: ft.TextField = None
tax_rate_input: ft.TextField = None
discount_rate_input: ft.TextField = None

all_input_text_fields_list = [] # Populated in main()

def update_assumptions_and_run(e):
    global current_assumptions, latest_cash_flow_df, latest_kpis, latest_sensitivity_df
    
    parsing_errors_found = False

    def get_val(tf: ft.TextField, name: str, is_int=False, is_percentage=False):
        nonlocal parsing_errors_found
        tf.error_text = "" # Clear previous error
        value_str = tf.value.strip()
        if not value_str:
            tf.error_text = f"{name} is required."
            parsing_errors_found = True
            return None
        try:
            val = int(value_str) if is_int else float(value_str)
            if is_percentage:
                val /= 100.0
            return val
        except ValueError:
            tf.error_text = f"Invalid { 'integer' if is_int else 'number' } for {name}."
            parsing_errors_found = True
            return None

    for field_tf in all_input_text_fields_list: # Clear all errors first
        if field_tf: field_tf.error_text = ""

    temp_assumptions = {}
    temp_assumptions['location_name'] = location_profile_dropdown.value
    temp_assumptions['ppa_price_eur_kwh'] = get_val(ppa_price_input, "PPA Price")
    temp_assumptions['production_mwh_year1'] = get_val(prod_input, "Production")
    temp_assumptions['opex_keuros_year1'] = get_val(opex_input, "OPEX")
    temp_assumptions['capex_meur'] = get_val(capex_input, "CAPEX")
    temp_assumptions['grant_meur_italy'] = get_val(grant_italy_input, "Grant Italy")
    temp_assumptions['grant_meur_masen'] = get_val(grant_masen_input, "Grant MASEN")
    temp_assumptions['grant_meur_iresen'] = get_val(grant_iresen_input, "Grant IRESEN")
    temp_assumptions['grant_meur_connection'] = get_val(grant_connection_input, "Grant Connection")
    temp_assumptions['years'] = get_val(years_input, "Project Years", is_int=True)
    temp_assumptions['degradation'] = get_val(degradation_input, "Degradation", is_percentage=True)
    temp_assumptions['price_escalation'] = get_val(price_escalation_input, "PPA Escal.", is_percentage=True)
    temp_assumptions['opex_escalation'] = get_val(opex_escalation_input, "OPEX Escal.", is_percentage=True)
    temp_assumptions['debt_ratio'] = get_val(debt_ratio_input, "Debt Ratio", is_percentage=True)
    temp_assumptions['interest_rate'] = get_val(interest_rate_input, "Interest Rate", is_percentage=True)
    temp_assumptions['debt_years'] = get_val(debt_years_input, "Debt Years", is_int=True)
    temp_assumptions['grace_years'] = get_val(grace_years_input, "Grace Years", is_int=True)
    temp_assumptions['tax_holiday'] = get_val(tax_holiday_input, "Tax Holiday", is_int=True)
    temp_assumptions['tax_rate'] = get_val(tax_rate_input, "Tax Rate", is_percentage=True)
    temp_assumptions['discount_rate'] = get_val(discount_rate_input, "Discount Rate", is_percentage=True)

    if parsing_errors_found:
        kpi_display_ref.value = "Please correct errors in the input fields."
        kpi_display_ref.color = ft.Colors.RED
        page_ref.update(*all_input_text_fields_list, kpi_display_ref)
        return

    try:
        current_assumptions = Assumptions(**temp_assumptions)
    except Exception as ex_init: # Catch potential errors during Assumptions instantiation
        kpi_display_ref.value = f"Error initializing assumptions: {ex_init}"
        kpi_display_ref.color = ft.Colors.RED
        page_ref.update(kpi_display_ref)
        return
        
    try:
        cash_flow_df = build_cashflow(current_assumptions)
        kpis_result = compute_kpis(cash_flow_df, current_assumptions)
        sensitivity_df_result = build_sensitivity(current_assumptions, "ppa_price_eur_kwh")

        latest_cash_flow_df = cash_flow_df
        latest_kpis = kpis_result
        latest_sensitivity_df = sensitivity_df_result
        
        kpi_list = [f"{key}: {value:.2f}" if isinstance(value, float) and not (math.isinf(value) or math.isnan(value)) else f"{key}: {str(value)}" for key, value in kpis_result.items()]
        kpi_display_ref.value = f"KPIs ({current_assumptions.location_name}):\n" + "\n".join(kpi_list)
        kpi_display_ref.color = None 
        
        show_fcfe_chart(None) # This also calls page_ref.update()
    except Exception as ex_calc:
        kpi_display_ref.value = f"Error during calculation: {ex_calc}"
        kpi_display_ref.color = ft.Colors.RED
        latest_cash_flow_df = None; latest_kpis = None; latest_sensitivity_df = None
        global current_fig_generator, current_fig_args, current_fig_title
        current_fig_generator = None; current_fig_args = (); current_fig_title = ""
        page_ref.update(kpi_display_ref)

def show_chart(fig_generator_func, title="Chart", *args):
    global current_fig_generator, current_fig_args, current_fig_title
    plt.close('all')
    if page_ref: page_ref.splash = ft.ProgressBar(); page_ref.update()
    try:
        fig = fig_generator_func(*args)
        if fig and chart_display:
            chart_display.figure = fig
            chart_display.update() 
            if kpi_display_ref: kpi_display_ref.value = f"Displaying: {title}" 
            current_fig_generator, current_fig_args, current_fig_title = fig_generator_func, args, title
        elif kpi_display_ref:
            kpi_display_ref.value = f"Could not generate: {title}"
            current_fig_generator, current_fig_args, current_fig_title = None, (), ""
    except Exception as ex:
        if kpi_display_ref: kpi_display_ref.value = f"Error generating {title}: {ex}"
        current_fig_generator, current_fig_args, current_fig_title = None, (), ""
    if page_ref: page_ref.splash = None; page_ref.update()

def get_cashflow_and_sensitivity():
    cf_df = build_cashflow(current_assumptions)
    sens_df = build_sensitivity(current_assumptions, "ppa_price_eur_kwh")
    return cf_df, sens_df

def show_fcfe_chart(e): cf_df, _ = get_cashflow_and_sensitivity(); show_chart(generate_fcfe_cumule_fig, "FCFE Cumulé", cf_df)
def show_dscr_chart(e): cf_df, _ = get_cashflow_and_sensitivity(); show_chart(generate_dscr_fig, "DSCR", cf_df)
def show_sens_tri_chart(e): _, sens_df = get_cashflow_and_sensitivity(); show_chart(generate_sens_tri_fig, "Sensibilité TRI", sens_df)
def show_structure_financement_chart(e): cf_df, _ = get_cashflow_and_sensitivity(); show_chart(generate_structure_financement_pie_fig, "Structure Financement", cf_df)
def show_scenario_comparison_chart(e): show_chart(generate_scenario_comparison_fig, "Comparaison Scénarios")
def show_gantt_chart(e): show_chart(generate_gantt_chart_fig, "Gantt Chart")
def show_lcoe_waterfall_chart(e): show_chart(generate_lcoe_waterfall_fig, "LCOE Waterfall")

def profile_change_handler(e):
    selected_profile_name = e.control.value
    profile_data = DEFAULT_PROFILES.get(selected_profile_name, DEFAULT_PROFILES["Custom/Manual"])
    
    fields_to_set = [
        (prod_input, profile_data.get("production_mwh_year1", 0.0)),
        (opex_input, profile_data.get("opex_keuros_year1", 0.0)),
        (capex_input, profile_data.get("capex_meur", 0.0)),
        (grant_masen_input, profile_data.get("grant_meur_masen", 0.0)),
        (grant_iresen_input, profile_data.get("grant_meur_iresen", 0.0)),
        (grant_connection_input, profile_data.get("grant_meur_connection", 0.0)),
        (grant_italy_input, profile_data.get("grant_meur_italy", 0.0))
    ]
    updated_controls = []
    for field_tf, val in fields_to_set:
        if field_tf:
            field_tf.value = str(val)
            field_tf.error_text = "" 
            updated_controls.append(field_tf)
    if page_ref and updated_controls: page_ref.update(*updated_controls)

def save_picked_directory(e: ft.FilePickerResultEvent):
    global selected_output_path
    if e.path: selected_output_path = Path(e.path)
    else: selected_output_path = None
    if selected_folder_text_ref: 
        selected_folder_text_ref.value = f"Output folder: {selected_output_path}" if selected_output_path else "Output folder: Not selected"
    if page_ref and selected_folder_text_ref : page_ref.update(selected_folder_text_ref)

def export_results_to_excel(e=None):
    if not selected_output_path:
        kpi_display_ref.value = "Select output folder first."; kpi_display_ref.color = ft.Colors.RED
        if page_ref: page_ref.update(kpi_display_ref); return
    if latest_cash_flow_df is None:
        kpi_display_ref.value = "No model results. Run model first."; kpi_display_ref.color = ft.Colors.RED
        if page_ref: page_ref.update(kpi_display_ref); return
    try:
        dest_file = selected_output_path / "financial_model_output.xlsx"
        export_excel_logic(current_assumptions, latest_cash_flow_df, latest_kpis, latest_sensitivity_df, dest_file)
        kpi_display_ref.value = f"Excel exported to: {dest_file}"; kpi_display_ref.color = ft.Colors.GREEN
    except Exception as ex:
        kpi_display_ref.value = f"Error exporting Excel: {ex}"; kpi_display_ref.color = ft.Colors.RED
    if page_ref: page_ref.update(kpi_display_ref)

def save_current_chart_to_file(e=None):
    if not selected_output_path:
        kpi_display_ref.value = "Select output folder first."; kpi_display_ref.color = ft.Colors.RED
        if page_ref: page_ref.update(kpi_display_ref); return
    if not current_fig_generator:
        kpi_display_ref.value = "No chart to save."; kpi_display_ref.color = ft.Colors.RED
        if page_ref: page_ref.update(kpi_display_ref); return
    try:
        safe_title = "".join(c for c in current_fig_title if c.isalnum() or c in " _-").strip().replace(" ", "_").lower() or "chart"
        dest_file = selected_output_path / f"{safe_title}.png"
        fig = current_fig_generator(*current_fig_args, output_path=dest_file) # Generators save if path is given
        if fig is not None: # Fallback if generator didn't save and returned fig
             plt.figure(fig.number); plt.savefig(dest_file, bbox_inches='tight'); plt.close(fig)
        kpi_display_ref.value = f"Chart '{current_fig_title}' saved to: {dest_file}"; kpi_display_ref.color = ft.Colors.GREEN
    except Exception as ex:
        kpi_display_ref.value = f"Error saving chart: {ex}"; kpi_display_ref.color = ft.Colors.RED
    if page_ref: page_ref.update(kpi_display_ref)

def main(p: ft.Page):
    global page_ref, selected_folder_text_ref, kpi_display_ref, chart_display
    global location_profile_dropdown, ppa_price_input, prod_input, opex_input, capex_input
    global grant_masen_input, grant_iresen_input, grant_connection_input, grant_italy_input
    global years_input, degradation_input, price_escalation_input, opex_escalation_input
    global debt_ratio_input, interest_rate_input, debt_years_input, grace_years_input
    global tax_holiday_input, tax_rate_input, discount_rate_input, all_input_text_fields_list

    page_ref = p
    page_ref.title = "🌞 Solar Financial Modeling Suite"
    page_ref.theme_mode = ft.ThemeMode.LIGHT
    page_ref.theme = ft.Theme(
        color_scheme_seed=ft.Colors.BLUE,
        visual_density=ft.VisualDensity.COMFORTABLE
    )
    page_ref.vertical_alignment = ft.MainAxisAlignment.START
    page_ref.horizontal_alignment = ft.CrossAxisAlignment.START
    page_ref.scroll = ft.ScrollMode.AUTO
    page_ref.auto_scroll = True
    page_ref.padding = 20
    page_ref.bgcolor = ft.Colors.GREY_50

    chart_display = MatplotlibChart(expand=True)
    kpi_display_local = ft.Container(
        content=ft.Text("📊 KPIs will be shown here...", size=16, selectable=True, color=ft.Colors.GREY_700),
        padding=20,
        bgcolor=ft.Colors.WHITE,
        border_radius=10,
        border=ft.border.all(1, ft.Colors.BLUE_200),
        shadow=ft.BoxShadow(
            spread_radius=1,
            blur_radius=5,
            color=ft.Colors.BLUE_100,
            offset=ft.Offset(0, 2)
        )
    )
    kpi_display_ref = kpi_display_local.content

    # --- Define UI Controls ---
    tf_width_short = 140
    tf_width_medium = 170
    tf_width_long = 200
    
    # Enhanced styling for input fields
    def create_styled_textfield(label, value, width, **kwargs):
        return ft.TextField(
            label=label,
            value=str(value),
            width=width,
            border_radius=8,
            filled=True,
            bgcolor=ft.Colors.WHITE,
            border_color=ft.Colors.BLUE_200,
            focused_border_color=ft.Colors.BLUE_400,
            **kwargs
        )
    
    location_profile_dropdown = ft.Dropdown(
        label="📍 Location Profile", 
        options=[ft.dropdown.Option(name) for name in DEFAULT_PROFILES.keys()], 
        value=current_assumptions.location_name, 
        on_change=profile_change_handler, 
        width=220,
        border_radius=8,
        filled=True,
        bgcolor=ft.Colors.WHITE,
        border_color=ft.Colors.BLUE_200,
        focused_border_color=ft.Colors.BLUE_400
    )
    
    ppa_price_input = create_styled_textfield("💰 PPA Price (€/kWh)", current_assumptions.ppa_price_eur_kwh, tf_width_long)
    prod_input = create_styled_textfield("⚡ Production (MWh y1)", current_assumptions.production_mwh_year1, tf_width_medium)
    opex_input = create_styled_textfield("🔧 OPEX (€k y1)", current_assumptions.opex_keuros_year1, tf_width_medium)
    capex_input = create_styled_textfield("🏗️ CAPEX (M€)", current_assumptions.capex_meur, tf_width_short)
    
    grant_masen_input = create_styled_textfield("🏛️ Grant MASEN (M€)", current_assumptions.grant_meur_masen, tf_width_long)
    grant_iresen_input = create_styled_textfield("🔬 Grant IRESEN (M€)", current_assumptions.grant_meur_iresen, tf_width_long)
    grant_connection_input = create_styled_textfield("🔌 Grant Connection (M€)", current_assumptions.grant_meur_connection, tf_width_long)
    grant_italy_input = create_styled_textfield("🇮🇹 Grant Italy (M€)", current_assumptions.grant_meur_italy, tf_width_long)

    years_input = create_styled_textfield("📅 Project Years", current_assumptions.years, tf_width_short, input_filter=ft.InputFilter(allow=True, regex_string=r"^[0-9]*$"))
    degradation_input = create_styled_textfield("📉 Degradation (%/yr)", current_assumptions.degradation * 100, tf_width_medium, hint_text="e.g. 0.4", suffix_text="%")
    price_escalation_input = create_styled_textfield("📈 PPA Escalation (%/yr)", current_assumptions.price_escalation * 100, tf_width_long, hint_text="e.g. 2.0", suffix_text="%")
    opex_escalation_input = create_styled_textfield("🔧📈 OPEX Escalation (%/yr)", current_assumptions.opex_escalation * 100, tf_width_long, hint_text="e.g. 2.0", suffix_text="%")

    debt_ratio_input = create_styled_textfield("💳 Debt Ratio (%)", current_assumptions.debt_ratio * 100, tf_width_medium, hint_text="e.g. 60.0", suffix_text="%")
    interest_rate_input = create_styled_textfield("📊 Interest Rate (%/yr)", current_assumptions.interest_rate * 100, tf_width_long, hint_text="e.g. 2.5", suffix_text="%")
    debt_years_input = create_styled_textfield("⏰ Debt Years", current_assumptions.debt_years, tf_width_short, input_filter=ft.InputFilter(allow=True, regex_string=r"^[0-9]*$"))
    grace_years_input = create_styled_textfield("⏳ Grace Years", current_assumptions.grace_years, tf_width_short, input_filter=ft.InputFilter(allow=True, regex_string=r"^[0-9]*$"))

    tax_holiday_input = create_styled_textfield("🏖️ Tax Holiday (Years)", current_assumptions.tax_holiday, tf_width_medium, input_filter=ft.InputFilter(allow=True, regex_string=r"^[0-9]*$"))
    tax_rate_input = create_styled_textfield("💸 Tax Rate (%)", current_assumptions.tax_rate * 100, tf_width_medium, hint_text="e.g. 15.0", suffix_text="%")
    discount_rate_input = create_styled_textfield("📉 Discount Rate (%)", current_assumptions.discount_rate * 100, tf_width_long, hint_text="e.g. 8.0", suffix_text="%")
    
    all_input_text_fields_list.extend([
        ppa_price_input, prod_input, opex_input, capex_input, grant_masen_input, grant_iresen_input,
        grant_connection_input, grant_italy_input, years_input, degradation_input, price_escalation_input,
        opex_escalation_input, debt_ratio_input, interest_rate_input, debt_years_input, grace_years_input,
        tax_holiday_input, tax_rate_input, discount_rate_input
    ])

    # Enhanced button styling
    def create_primary_button(text, on_click, icon=None, width=None):
        return ft.ElevatedButton(
            text=text,
            on_click=on_click,
            icon=icon,
            height=45,
            width=width,
            style=ft.ButtonStyle(
                bgcolor=ft.Colors.BLUE_600,
                color=ft.Colors.WHITE,
                elevation=3,
                shape=ft.RoundedRectangleBorder(radius=8)
            )
        )
    
    def create_secondary_button(text, on_click, icon=None, width=None):
        return ft.ElevatedButton(
            text=text,
            on_click=on_click,
            icon=icon,
            height=40,
            width=width,
            style=ft.ButtonStyle(
                bgcolor=ft.Colors.GREY_100,
                color=ft.Colors.GREY_800,
                elevation=2,
                shape=ft.RoundedRectangleBorder(radius=8)
            )
        )
    
    run_button = create_primary_button("🚀 Run Model & Update KPIs", update_assumptions_and_run, width=250)
    output_directory_picker = ft.FilePicker(on_result=save_picked_directory)
    page_ref.overlay.append(output_directory_picker)
    select_folder_button = create_secondary_button("📁 Select Output Folder", lambda _: output_directory_picker.get_directory_path(dialog_title="Select Output Folder"), ft.Icons.FOLDER_OPEN)
    selected_folder_text_local = ft.Text("📂 Output folder: Not selected", color=ft.Colors.GREY_600, size=14)
    selected_folder_text_ref = selected_folder_text_local
    export_excel_button = create_secondary_button("📊 Export to Excel", export_results_to_excel, ft.Icons.TABLE_CHART)
    save_chart_button = create_secondary_button("💾 Save Current Chart", save_current_chart_to_file, ft.Icons.SAVE)

    # --- Layout ---
    def create_section_card(title, content, icon=""):
        return ft.Container(
            content=ft.Column([
                ft.Text(f"{icon} {title}", size=18, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_800),
                ft.Divider(height=1, color=ft.Colors.BLUE_200),
                content
            ], spacing=10),
            padding=20,
            bgcolor=ft.Colors.WHITE,
            border_radius=12,
            border=ft.border.all(1, ft.Colors.BLUE_100),
            shadow=ft.BoxShadow(
                spread_radius=1,
                blur_radius=8,
                color=ft.Colors.BLUE_50,
                offset=ft.Offset(0, 2)
            )
        )
    
    # Core financials section
    core_financials = create_section_card(
        "Location & Core Financials", 
        ft.Column([
            ft.Row([location_profile_dropdown, ppa_price_input], alignment=ft.MainAxisAlignment.START, spacing=15),
            ft.Row([prod_input, opex_input, capex_input], alignment=ft.MainAxisAlignment.START, spacing=15)
        ], spacing=15),
        "🏢"
    )
    
    # Grants section
    grants_section = create_section_card(
        "Grant Funding (M€)",
        ft.Column([
            ft.Row([grant_masen_input, grant_iresen_input], alignment=ft.MainAxisAlignment.START, spacing=15),
            ft.Row([grant_connection_input, grant_italy_input], alignment=ft.MainAxisAlignment.START, spacing=15)
        ], spacing=15),
        "💰"
    )
    
    # General parameters section
    general_params = create_section_card(
        "General Parameters",
        ft.Column([
            ft.Row([years_input, degradation_input], alignment=ft.MainAxisAlignment.START, spacing=15),
            ft.Row([price_escalation_input, opex_escalation_input], alignment=ft.MainAxisAlignment.START, spacing=15)
        ], spacing=15),
        "⚙️"
    )
    
    # Debt parameters section
    debt_params = create_section_card(
        "Debt Parameters",
        ft.Column([
            ft.Row([debt_ratio_input, interest_rate_input], alignment=ft.MainAxisAlignment.START, spacing=15),
            ft.Row([debt_years_input, grace_years_input], alignment=ft.MainAxisAlignment.START, spacing=15)
        ], spacing=15),
        "🏦"
    )
    
    # Tax parameters section
    tax_params = create_section_card(
        "Tax & Discount Parameters",
        ft.Row([tax_holiday_input, tax_rate_input, discount_rate_input], alignment=ft.MainAxisAlignment.START, spacing=15),
        "📊"
    )
    
    inputs_layout = ft.Column([
        core_financials,
        grants_section,
        general_params,
        debt_params,
        tax_params,
        ft.Container(
            content=ft.Row([run_button], alignment=ft.MainAxisAlignment.CENTER),
            padding=ft.padding.symmetric(vertical=20)
        )
    ], spacing=20, width=900, scroll=ft.ScrollMode.AUTO)

    # Output actions section
    output_actions_layout = create_section_card(
        "Export & Save Options",
        ft.Column([
            ft.Row([select_folder_button, export_excel_button], alignment=ft.MainAxisAlignment.START, spacing=15),
            selected_folder_text_local,
            ft.Row([save_chart_button], alignment=ft.MainAxisAlignment.START)
        ], spacing=15),
        "💾"
    )
    
    top_controls_column = ft.Column([inputs_layout, output_actions_layout], spacing=20, scroll=ft.ScrollMode.AUTO)

    # Enhanced chart buttons
    def create_chart_button(text, on_click, icon="📊"):
        return ft.ElevatedButton(
            text=f"{icon} {text}",
            on_click=on_click,
            width=280,
            height=45,
            style=ft.ButtonStyle(
                bgcolor=ft.Colors.BLUE_50,
                color=ft.Colors.BLUE_800,
                elevation=2,
                shape=ft.RoundedRectangleBorder(radius=8),
                side=ft.BorderSide(1, ft.Colors.BLUE_200)
            )
        )

    chart_buttons_list = [
        create_chart_button("FCFE Cumulé", show_fcfe_chart, "💰"),
        create_chart_button("DSCR", show_dscr_chart, "📈"),
        create_chart_button("Sensibilité TRI", show_sens_tri_chart, "🎯"),
        create_chart_button("Structure Financement", show_structure_financement_chart, "🥧"),
        create_chart_button("Comparaison Scénarios", show_scenario_comparison_chart, "⚖️"),
        create_chart_button("Gantt Chart", show_gantt_chart, "📅"),
        create_chart_button("LCOE Waterfall", show_lcoe_waterfall_chart, "🌊"),
    ]
    
    chart_buttons_column = ft.Column([
        ft.Text("📊 Chart Selection", size=18, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_800)
    ] + chart_buttons_list, spacing=12)

    sidebar_container = ft.Container(
        content=chart_buttons_column, 
        width=320, 
        padding=20,
        bgcolor=ft.Colors.WHITE,
        border_radius=12,
        border=ft.border.all(1, ft.Colors.BLUE_100),
        shadow=ft.BoxShadow(
            spread_radius=1,
            blur_radius=8,
            color=ft.Colors.BLUE_50,
            offset=ft.Offset(0, 2)
        )
    )

    # Enhanced chart display area
    chart_container = ft.Container(
        content=chart_display,
        bgcolor=ft.Colors.WHITE,
        border_radius=12,
        border=ft.border.all(1, ft.Colors.BLUE_100),
        padding=10,
        shadow=ft.BoxShadow(
            spread_radius=1,
            blur_radius=8,
            color=ft.Colors.BLUE_50,
            offset=ft.Offset(0, 2)
        )
    )
    
    main_content_area = ft.Column([
        kpi_display_local, 
        chart_container
    ], expand=True, alignment=ft.MainAxisAlignment.START, horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=20)

    # Header section
    header = ft.Container(
        content=ft.Column([
            ft.Text("🌞 Solar Financial Modeling Suite", size=32, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_800),
            ft.Text("Advanced financial analysis for solar energy projects", size=16, color=ft.Colors.GREY_600, italic=True)
        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=5),
        padding=ft.padding.symmetric(vertical=20),
        bgcolor=ft.Colors.WHITE,
        border_radius=12,
        border=ft.border.all(1, ft.Colors.BLUE_100),
        shadow=ft.BoxShadow(
            spread_radius=1,
            blur_radius=8,
            color=ft.Colors.BLUE_50,
            offset=ft.Offset(0, 2)
        )
    )

    # Main layout with responsive design
    main_row = ft.Row([
        sidebar_container, 
        ft.Container(content=main_content_area, expand=True, padding=ft.padding.only(left=20))
    ], vertical_alignment=ft.CrossAxisAlignment.START, expand=True)

    # Wrap everything in a scrollable container
    main_content = ft.Column([
        header,
        ft.Container(height=20),  # Spacer
        top_controls_column,
        ft.Container(height=20),  # Spacer
        main_row
    ], scroll=ft.ScrollMode.AUTO, expand=True)
    
    page_ref.add(main_content)
    
    profile_change_handler(ft.ControlEvent(target=None, name=None, data=None, control=location_profile_dropdown, page=None)) # Initial population
    update_assumptions_and_run(None)

if __name__ == "__main__":
    ft.app(target=main)